import { env, fetchMock } from 'cloudflare:test';
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import app from '../../src/api';
import { setupGetUserMock } from '../mocks';

describe('Kaku API', () => {
  beforeEach(() => {
    fetchMock.activate();
  });

  afterEach(() => {
    fetchMock.deactivate();
  });

  // Test the root endpoint
  it('should return 200 and welcome message at the root path', async () => {
    // Create a request to the root path
    const request = new Request('http://localhost:8787/', {
      headers: {
        Authorization: 'test-token',
      },
    });

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Check response
    expect(response.status).toBe(200);
    expect(await response.text()).toBe('Kaku - Cloudflare Agents API');
  });

  // Test the 404 response for non-existent routes
  it('should return 404 for non-existent paths', async () => {
    // Create a request to a non-existent path (not an agent route)
    const request = new Request('http://localhost:8787/non-existent-path', {
      headers: {
        Authorization: 'test-token',
      },
    });

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Check response
    expect(response.status).toBe(404);
  });

  // Test accessing agent route without auth
  it('should return 401 when accessing agent route without authorization', async () => {
    // Create a request to an agent route without auth
    const request = new Request('http://localhost:8787/agents/connections/test-id-1');

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Check response - should be 401 Unauthorized due to missing auth token
    expect(response.status).toBe(401);
  });

  // Test accessing agent route with auth
  it('should return 200 when accessing agent route with authorization', async () => {
    setupGetUserMock('u_test-id-1');

    // Create a request to an agent route with auth
    const request = new Request(
      'http://localhost:8787/agents/connections/test-id-1?token=magic-link-token-test-example',
      {
        headers: {
          'content-type': 'application/json',
        },
      },
    );

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Check response - agent routes should return successful responses
    expect(response.status).toBe(200);
  });

  // Test the root API without auth
  it('should still allow access to the root API without authorization', async () => {
    // Create a request to the root path without auth
    const request = new Request('http://localhost:8787/');

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Root path should be accessible without auth
    expect(response.status).toBe(200);
    expect(await response.text()).toBe('Kaku - Cloudflare Agents API');
  });

  // test the protected API without auth
  it('should return 401 when accessing protected API without authorization', async () => {
    // Create a request to the protected API path without auth
    const request = new Request('http://localhost:8787/protected-api-example');

    // Call the app with the request
    const response = await app.fetch(request, env);

    // Check response - should be 401 Unauthorized due to missing auth token
    expect(response.status).toBe(401);
  });

  // test the protected API with auth
  it('should return 200 when accessing protected API with authorization', async () => {
    const userId = 'u_test-id-2';
    setupGetUserMock(userId, 200, { authType: 'x-auth' });

    // Create a request to the protected API path with auth
    const request = new Request('http://localhost:8787/protected-api-example', {
      headers: {
        'content-type': 'application/json',
        'x-auth-token': 'x-auth-token-test-example',
      },
    });

    // Call the app with the request
    const response = await app.fetch(request, env);

    expect(response.status).toBe(200);
    expect(await response.text()).toBe(
      `"Protected API example. User is authenticated. Userid: ${userId}"`,
    );
  });
});
