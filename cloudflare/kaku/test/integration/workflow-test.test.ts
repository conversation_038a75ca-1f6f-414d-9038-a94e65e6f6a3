import { env } from 'cloudflare:test';
import { describe, expect, it } from 'vitest';
import app from '../../src/api';
import { ConnectionsWorkflowParams } from '../../src/workflow/types';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../../src/common/utils/storeConnectionScreenshotToR2';

describe('should render the initial htmx correctly', () => {
  it('should render service page with provided userId and serviceId', async () => {
    const userId = 'testUser';
    const serviceId = 'facebook';
    const request = new Request(`http://localhost:8787/${userId}/${serviceId}`, { method: 'GET' });

    const response = await app.fetch(request, env);

    expect(response.status).toBe(200);
    const text = await response.text();

    // Check that the response contains the expected rendered HTML content
    expect(text).toContain('Facebook');
    expect(text).toContain(userId);
  });

  it('it should store screenshots to R2', async () => {
    //Arrange
    const userId = 'u_testuser1234';
    const serviceId = 'facebook';
    const sessionId = '1234';

    const connectionsWorkflowParams: ConnectionsWorkflowParams = {
      platformId: serviceId,
      userId: userId,
      sessionId: sessionId,
    };

    const screenshotInput = 'this is our fake screenshot';

    const workflowState = ConnectionWorkflowState.Authenticated;

    //Act
    await storeConnectionScreenshotToR2(
      env.SCREENSHOTS_INBOUND_BUCKET,
      userId,
      serviceId,
      sessionId,
      workflowState,
      screenshotInput,
    );

    //Assert
    const expectedPath = `base64_screenshots/${userId}/${serviceId}/${sessionId}_${workflowState}_`;

    const response = await env.SCREENSHOTS_INBOUND_BUCKET.list({
      startAfter: expectedPath,
    });

    const completeKey = response.objects[0].key;

    const screenshotOutput = await env.SCREENSHOTS_INBOUND_BUCKET.get(completeKey);

    if (screenshotOutput) {
      const blob = await screenshotOutput.blob();
      const arrayBuffer = await blob.arrayBuffer();
      const uint8 = new Uint8Array(arrayBuffer);

      expect(uint8).toStrictEqual(convertBase64ToBytes(screenshotInput));
    } else {
      //Fail the test, response shouldn't be null
      expect(true).toBe(false);
    }
  });
});

function convertBase64ToBytes(input: string): ArrayBuffer {
  const binaryString = atob(input);
  const len = binaryString.length;
  const bytes = new Uint8Array(len);
  for (let i = 0; i < len; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes as any;
}
