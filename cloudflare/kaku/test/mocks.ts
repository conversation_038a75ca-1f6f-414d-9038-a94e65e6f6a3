import { env, fetchMock } from 'cloudflare:test';
import {
  DataUploadStatus,
  NotificationOptions,
  User,
  UserRole,
  UserStatus,
  USLocale,
} from '../src/user/User';
import { Environment } from '../src';

declare module 'cloudflare:test' {
  interface ProvidedEnv extends Environment {}
}

export interface AuthType {
  authType: 'magic' | 'basic' | 'x-auth' | 'none' | 'wrong';
}

export const MAGIC_LINK_TOKEN = 'magic-link-token-test-example';
const BASIC_AUTH_TOKEN = 'basic-auth-token-test-example';
const X_AUTH_TOKEN = 'x-auth-token-test-example';

export const setupGetUserMock = (
  userId: string,
  status = 200,
  authType: AuthType = { authType: 'magic' },
) => {
  return fetchMock
    .get(env.SUNNY_API_ENDPOINT)
    .intercept({
      path: `v1/users`,
      headers: getMockHeaders(authType),
      method: 'get',
    })
    .reply(status, getUserById(userId));
};

export const getUserById = (userId: string): User => {
  return {
    userId: userId,
    phone: '************',
    username: 'john_doe',
    status: UserStatus.enum.ACTIVE,
    email: '<EMAIL>',
    avatarUrl: 'http://example.com/avatar.jpg',
    points: { dataPoints: 100, reputationPoints: 50, rewardPoints: 20 },
    isDemographicSurveyComplete: true,
    referralCode: { value: 'REF123' },
    inviterUserReferralCode: { value: 'REF123' },
    lpoaStatus: { status: 'GRANTED', value: '2023-01-01T00:00:00.000Z' },
    dataUploadStatus: DataUploadStatus.enum.UPLOADED,
    role: UserRole.enum.USER,
    poolConnections: 5,
    activeAgents: 2,
    isDeleted: false,
    tierId: 'tier1',
    countryLocale: { type: 'US', locale: USLocale.enum.en_US },
    hasWithdrawnInitialPayout: false,
    userSubscriptions: {},
    notifications: {
      emailNotifications: {
        productNews: NotificationOptions.enum.ENABLED,
        taskUpdates: NotificationOptions.enum.DISABLED,
        recommendedTasks: NotificationOptions.enum.DAILY,
      },
    },
    riskFlags: [],
    verificationStatus: { status: 'UNVERIFIED' },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
};

const getMockHeaders = (authType: AuthType): Record<string, string> => {
  switch (authType.authType) {
    case 'magic':
      fetchMock
        .get(env.SUNNY_API_ENDPOINT)
        .intercept({
          path: `v1/login`,
          headers: {
            authorization: `Magic ${MAGIC_LINK_TOKEN}`,
            'content-type': 'application/json',
          },
          method: 'post',
        })
        .reply(200, { xAuthToken: X_AUTH_TOKEN, csrfToken: '' });
      return {
        'x-auth-token': X_AUTH_TOKEN,
        'content-type': 'application/json',
      };
    case 'basic':
      return {
        authorization: `Basic ${BASIC_AUTH_TOKEN}`,
        'content-type': 'application/json',
      };
    case 'none':
      return {
        'content-type': 'application/json',
      };
    case 'wrong':
      return {
        authorization: `Basic wrong-token`,
        'content-type': 'application/json',
      };
    case 'x-auth':
      return {
        'x-auth-token': X_AUTH_TOKEN,
        'content-type': 'application/json',
      };
    default:
      return {};
  }
};
