/**
 * Tests for the screenshot comparison utility functions.
 * Tests performance and accuracy of the screenshot comparison functionality.
 */

import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';
import pixelmatch from 'pixelmatch';

// Import the screenshot comparison utilities directly
import screenshotComparisonUtils from '../../../src/client/utils/screenshot-comparison.mjs';

// Extract the utility functions
const {
  compareScreenshots,
  prepareBufferForComparison,
  createTestImage,
  createImageWithDifferences,
} = screenshotComparisonUtils;

// Set up the test environment
beforeAll(() => {
  // Define window if it doesn't exist in the test environment
  if (typeof window === 'undefined') {
    global.window = {};
  }

  // Define performance if it doesn't exist in the test environment
  if (typeof performance === 'undefined') {
    global.performance = {
      now: () => {
        return Date.now();
      },
    };
  }

  // Set up pixelmatch in the window object
  window.pixelmatch = (img1, img2, diff, width, height, options) => {
    // Use the imported pixelmatch
    return pixelmatch(img1, img2, diff, width, height, options);
  };

  // Make the utilities available on the window object for compatibility
  window.screenshotComparisonUtils = {
    compareScreenshots,
    prepareBufferForComparison,
    createTestImage,
    createImageWithDifferences,
  };

  console.log('Pixelmatch and screenshot comparison utilities set up successfully');
});

describe('Screenshot Comparison Utility', () => {
  // Test the compareScreenshots function
  describe('compareScreenshots', () => {
    it('should return 0% difference for identical images', () => {
      const img1 = createTestImage(100, 100, [255, 0, 0, 255]);
      const img2 = createTestImage(100, 100, [255, 0, 0, 255]);

      const result = compareScreenshots(img1, img2);

      expect(result.percentageDiff).toBe(0);
      expect(result.numDiffPixels).toBe(0);
      expect(result.comparisonTime).toBeGreaterThan(0);
    });

    it('should detect differences between images', () => {
      const img1 = createTestImage(100, 100, [255, 0, 0, 255]);
      const img2 = createImageWithDifferences(img1, 1000, [0, 255, 0, 255]);

      const result = compareScreenshots(img1, img2);

      expect(result.percentageDiff).toBeGreaterThan(0);
      expect(result.numDiffPixels).toBeGreaterThan(0);
      expect(result.comparisonTime).toBeGreaterThan(0);
    });

    it('should handle images with different dimensions', () => {
      const img1 = createTestImage(100, 100);
      const img2 = createTestImage(200, 200);

      const result = compareScreenshots(img1, img2);

      expect(result.percentageDiff).toBe(0);
      expect(result.numDiffPixels).toBe(0);
    });

    it('should handle missing image data', () => {
      const result = compareScreenshots(null, null);

      expect(result.percentageDiff).toBe(0);
      expect(result.numDiffPixels).toBe(0);
    });
  });

  // Test the prepareBufferForComparison function
  describe('prepareBufferForComparison', () => {
    it('should correctly prepare a buffer for comparison', () => {
      const width = 100;
      const height = 100;
      const rgbaBuffer = new Uint8Array(width * height * 4);

      // Fill with red pixels
      for (let i = 0; i < rgbaBuffer.length; i += 4) {
        rgbaBuffer[i] = 255; // R
        rgbaBuffer[i + 1] = 0; // G
        rgbaBuffer[i + 2] = 0; // B
        rgbaBuffer[i + 3] = 255; // A
      }

      const result = prepareBufferForComparison(rgbaBuffer, { width, height });

      expect(result.width).toBe(width);
      expect(result.height).toBe(height);
      expect(result.data.length).toBe(width * height * 4);
      expect(result.data instanceof Uint8ClampedArray).toBe(true);
    });

    it('should use default dimensions if not provided', () => {
      const rgbaBuffer = new Uint8Array(800 * 600 * 4);

      const result = prepareBufferForComparison(rgbaBuffer);

      expect(result.width).toBe(800);
      expect(result.height).toBe(600);
    });
  });

  // Test the sampling feature
  describe('Pixel Sampling', () => {
    it('should correctly use sampling when specified', () => {
      const width = 100;
      const height = 100;
      const img1 = createTestImage(width, height, [255, 0, 0, 255]);
      const img2 = createImageWithDifferences(img1, 1000, [0, 255, 0, 255]);

      const result = compareScreenshots(img1, img2, { sampling: 2 });

      expect(result.samplingUsed).toBe(2);
      expect(result.percentageDiff).toBeGreaterThan(0);
      expect(result.numDiffPixels).toBeGreaterThan(0);
    });

    it('should return similar results with and without sampling', () => {
      const width = 200;
      const height = 200;
      const img1 = createTestImage(width, height, [255, 0, 0, 255]);
      // Create an image with 10% different pixels
      const diffPixels = Math.floor(width * height * 0.1);
      const img2 = createImageWithDifferences(img1, diffPixels, [0, 255, 0, 255]);

      const resultNoSampling = compareScreenshots(img1, img2);
      const resultWithSampling = compareScreenshots(img1, img2, { sampling: 2 });

      // The percentage difference should be similar (within 2%)
      expect(
        Math.abs(resultNoSampling.percentageDiff - resultWithSampling.percentageDiff),
      ).toBeLessThan(2);

      // Sampling should be faster
      expect(resultWithSampling.comparisonTime).toBeLessThanOrEqual(
        resultNoSampling.comparisonTime * 1.2,
      );
    });

    it('should handle different sampling rates', () => {
      const width = 400;
      const height = 300;
      const img1 = createTestImage(width, height, [255, 0, 0, 255]);
      const diffPixels = Math.floor(width * height * 0.05); // 5% different pixels
      const img2 = createImageWithDifferences(img1, diffPixels, [0, 255, 0, 255]);

      const samplingRates = [1, 2, 4, 8];
      const results = [];

      for (const sampling of samplingRates) {
        const startTime = performance.now();
        const result = compareScreenshots(img1, img2, { sampling });
        const totalTime = performance.now() - startTime;

        results.push({
          sampling,
          percentageDiff: result.percentageDiff.toFixed(2) + '%',
          comparisonTime: result.comparisonTime.toFixed(2) + 'ms',
          totalTime: totalTime.toFixed(2) + 'ms',
        });

        // Basic assertions
        expect(result.samplingUsed).toBe(sampling);
        expect(result.comparisonTime).toBeGreaterThan(0);
        expect(result.percentageDiff).toBeGreaterThan(0);
      }

      // Log sampling performance results
      console.table(results);
    });
  });

  // Performance tests
  describe('Performance Tests', () => {
    it('should measure performance for different image sizes', () => {
      const sizes = [
        { width: 100, height: 100 },
        { width: 400, height: 300 },
        { width: 800, height: 600 },
        { width: 1280, height: 720 },
        { width: 1920, height: 1080 },
      ];

      const results = [];

      for (const size of sizes) {
        const { width, height } = size;
        const img1 = createTestImage(width, height, [255, 0, 0, 255]);
        const img2 = createImageWithDifferences(img1, Math.floor(width * height * 0.05)); // 5% different pixels

        // Ensure we have a valid performance object
        if (typeof performance === 'undefined' || typeof performance.now !== 'function') {
          console.warn('Performance API not available, using Date.now() as fallback');
          global.performance = {
            now: () => Date.now(),
          };
        }

        const startTime = performance.now();
        const result = compareScreenshots(img1, img2);
        const totalTime = performance.now() - startTime;

        results.push({
          size: `${width}x${height}`,
          pixelCount: width * height,
          comparisonTime: result.comparisonTime,
          totalTime,
          percentageDiff: result.percentageDiff,
        });

        // Basic assertions to ensure the test is working
        expect(result.comparisonTime).toBeGreaterThan(0);
        expect(result.percentageDiff).toBeGreaterThan(0);
      }

      // Log performance results
      console.table(results);
    });

    it('should measure performance with different amounts of difference', () => {
      const width = 800; // Reduced size for faster tests
      const height = 600;
      const baseImage = createTestImage(width, height, [255, 0, 0, 255]);
      const diffPercentages = [0.1, 1, 5, 10, 25, 50];

      const results = [];

      for (const diffPercentage of diffPercentages) {
        const diffPixels = Math.floor(width * height * (diffPercentage / 100));
        const diffImage = createImageWithDifferences(baseImage, diffPixels);

        // Ensure we have a valid performance object
        if (typeof performance === 'undefined' || typeof performance.now !== 'function') {
          console.warn('Performance API not available, using Date.now() as fallback');
          global.performance = {
            now: () => Date.now(),
          };
        }

        const startTime = performance.now();
        const result = compareScreenshots(baseImage, diffImage);
        const totalTime = performance.now() - startTime;

        results.push({
          diffPercentage: `${diffPercentage}%`,
          pixelsChanged: diffPixels,
          detectedDiffPercentage: result.percentageDiff.toFixed(2) + '%',
          comparisonTime: result.comparisonTime.toFixed(2) + 'ms',
          totalTime: totalTime.toFixed(2) + 'ms',
        });

        // Basic assertions
        expect(result.comparisonTime).toBeGreaterThan(0);
        expect(Math.abs(result.percentageDiff - diffPercentage)).toBeLessThan(1);
      }

      // Log performance results
      console.table(results);
    });

    it('should compare performance with and without sampling', () => {
      const sizes = [
        { width: 800, height: 600 },
        { width: 1280, height: 720 },
        { width: 1920, height: 1080 },
      ];

      const samplingRates = [1, 2, 4, 8];
      const results = [];

      for (const size of sizes) {
        const { width, height } = size;
        const img1 = createTestImage(width, height, [255, 0, 0, 255]);
        const img2 = createImageWithDifferences(img1, Math.floor(width * height * 0.05)); // 5% different pixels

        for (const sampling of samplingRates) {
          const startTime = performance.now();
          const result = compareScreenshots(img1, img2, { sampling });
          const totalTime = performance.now() - startTime;

          results.push({
            size: `${width}x${height}`,
            sampling,
            pixelCount: width * height,
            comparisonTime: result.comparisonTime.toFixed(2) + 'ms',
            totalTime: totalTime.toFixed(2) + 'ms',
            percentageDiff: result.percentageDiff.toFixed(2) + '%',
          });
        }
      }

      // Log performance results
      console.table(results);
    });
  });
});
