import { imageToBase64 } from '../common/ImageHelpers';
import OpenAI from 'openai';
import { getLLMResponse } from '../../src/llm/llm-call';
import { instructions } from '../../src/workflow/utils/constants';

export async function testOpenAIFormGeneration() {
  const openAIKey = ''; //Insert the openAI key here to run this script

  const facebookLoginScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/facebook_login_screenshot.png')}`,
  );

  //Check to confirm that the user has added the openAI key
  if (!openAIKey) {
    console.log('Insert the openAI api key to continue..');
    return;
  }
  const client = new OpenAI({ apiKey: openAIKey });

  console.log('Loading...');

  const llmResponse = await getLLMResponse(client, facebookLoginScreenshot, instructions, 800, 600);

  console.log(`LLM Response ${JSON.stringify(llmResponse.result)}\n`);
  console.log(`The api call took: ${llmResponse.callDuration}ms`);
}

const action = process.argv[2];

switch (action) {
  case 'testOpenAIFormGeneration':
    testOpenAIFormGeneration();
    break;
  default:
    console.error(`Unknown or missing action: "${action}"`);
    process.exit(1);
}
