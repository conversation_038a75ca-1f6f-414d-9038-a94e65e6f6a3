# Simple-CDP Migration

## Overview

We migrated from `@cloudflare/puppeteer` to a custom `simple-cdp` implementation for better performance and smaller bundle sizes in our Cloudflare Workers environment.

## Bundle Size Comparison

| Library                    | Size   | Dependencies        |
| -------------------------- | ------ | ------------------- |
| @cloudflare/puppeteer-core | 440 KB | 15+ transitive deps |
| simple-cdp                 | 4 KB   | 0 dependencies      |

**Result: 99% size reduction**

## Why We Migrated

- **Bundle constraints**: Cloudflare Workers have size limits
- **Performance**: Faster cold starts and lower memory usage
- **Direct control**: We were already using CDP layer through puppeteer
- **Simplicity**: Only need basic CDP operations
- **Compatibility**: Issue with @cloudflare/puppeteer with latest wrangler

## Implementation Differences

### Before (Puppeteer)

```javascript
const browser = await puppeteer.connect({
  browserWSEndpoint: wsEndpoint,
});
const page = await browser.newPage();
const cdpSession = await page.createCDPSession();

// Navigation
await cdpSession.send('Page.navigate', { url });

// Cookies
await page.setCookie(...cookies);
```

### After (Simple-CDP)

```javascript
const cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });
cdpClient.addEventListener('attachedToTarget', handler); // where handler is a callback to get the sessionId
const targetInfo = await CDP.createTarget();
await cdpClient.Target.attachToTarget({
  targetId: targetInfo.id,
  flatten: true,
});

// Navigation
await cdpClient.Page.navigate({ url }, sessionId);

// Cookies
const response = await cdpClient.Storage.getCookies({}, sessionId);
```

## Key Changes

### 1. Connection Management

- **Before**: `puppeteer.connect()` → `page.createCDPSession()`
- **After**: Direct WebSocket connection with target attachment

### 2. Method Calls

- **Before**: High-level abstractions (`page.goto()`, `page.click()`) / (`cdpSession.send()`)
- **After**: Direct CDP protocol calls (`Page.navigate()`, `Input.dispatchMouseEvent()`)

### 3. Session Handling

- **Before**: Implicit session management
- **After**: Explicit `sessionId` parameter for all operations

### 4. Cookie Operations

- **Before**: `page.cookies()` / `page.setCookie()`
- **After**: `Storage.getCookies()` / `Storage.setCookies()` (updated to current CDP spec)

## Files Updated

- `src/browser/simple-cdp.ts` - Core CDP client implementation
- `src/client/browser-controller.ts` - Browser automation layer
- `src/workflow/adapters/CDPBrowserDataAdapter.ts` - Cookie/storage operations
- `src/workflow/connections-workflow.ts` - Main workflow integration
- `src/api/index.ts` - API endpoint integration

## Benefits Achieved

✅ **99% smaller bundle** (440 KB → 4 KB)  
✅ **Zero dependencies** (eliminated 15+ transitive deps)  
✅ **Faster cold starts** in Workers environment  
✅ **Lower memory usage** during runtime  
✅ **Direct CDP access** with latest protocol features  
✅ **Better error handling** with raw CDP responses

## Migration Notes

- All existing functionality preserved
- Updated deprecated `Network.getCookies` to `Storage.getCookies`
- Added explicit session management throughout
- Maintained compatibility with existing browser automation flows
