{"name": "kaku", "scripts": {"dev": "npm run build-client && wrangler dev", "deploy": "npm run build-client && npm run tailwind:build && wrangler deploy --minify", "tailwind:build": "tailwindcss -i src/main.css -o public/css/styles.css", "tailwind:watch": "tailwindcss -i src/main.css -o public/css/styles.css --watch", "wrangler:dev": "wrangler dev", "cf:typegen": "wrangler types --env-interface Env", "test": "npm run test:node && npm run test:workers", "test:node": "vitest run --config vitest.unit.config.ts", "test:workers": "vitest run --config vitest.integration.config.ts", "test-form-generation": "node --import tsx test/scripts/measureLLMCallDuration.ts testOpenAIFormGeneration", "test:watch": "vitest", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\"", "build-client": "rm -rf public/out && rollup -c --treeshake", "watch": "chokidar 'src/client/**/*.{mjs,ts}' -c 'npm run build-client'"}, "dependencies": {"@hyperbrowser/sdk": "^0.44.0", "@twind/core": "^1.1.3", "@twind/preset-tailwind": "^1.1.4", "agents": "^0.0.54", "argon2-wasm-edge": "^1.0.23", "hono": "^4.7.5", "hono-agents": "^0.0.44", "openai": "^4.87.1", "tsx": "^4.19.4"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.2", "@cloudflare/workers-types": "^4.20250407.0", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.2", "chokidar-cli": "^3.0.0", "devtools-protocol": "^0.0.1473885", "esbuild": "^0.19.0", "pixelmatch": "^7.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "rollup": "^4.40.0", "tailwindcss": "^3.4.17", "terser": "^5.30.4", "tslib": "^2.8.1", "typescript": "4.8.4", "vite": "^5.0.0", "vitest": "3.0.9", "wrangler": "4.19.1"}}