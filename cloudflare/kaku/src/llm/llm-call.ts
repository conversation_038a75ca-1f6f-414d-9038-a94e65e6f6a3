import { LLMResponse } from './types/llm-response';
import OpenA<PERSON> from 'openai';

type RaceLLMCallsParams = {
  client: OpenAI;
  screenshot: string;
  prompt: string;
  viewportWidth: number | undefined;
  viewportHeight: number | undefined;
  noOfConcurrentCalls: number;
  enable: boolean;
};
export async function raceLLMCalls({
  client,
  screenshot,
  prompt,
  viewportWidth,
  viewportHeight,
  noOfConcurrentCalls = 2,
  enable,
}: RaceLLMCallsParams): Promise<LLMResponse> {
  const contestants = Array.from({ length: enable ? noOfConcurrentCalls : 1 }, (_, i) =>
    getLLMResponse(client, screenshot, prompt, viewportWidth, viewportHeight),
  );

  const winner = await Promise.race(contestants);

  console.log(`The winning openai call took ${winner.callDuration}`);
  console.log(`Response ${winner.result}`);

  return winner;
}

export async function getLLMResponse(
  client: OpenAI,
  screenshot: string,
  prompt: string,
  viewportWidth: number | undefined,
  viewportHeight: number | undefined,
): Promise<LLMResponse> {
  const start = Date.now();

  const response = await client.responses.create({
    model: 'computer-use-preview',
    truncation: 'auto',
    tools: [
      {
        type: 'computer-preview',
        display_width: viewportWidth ?? 800,
        display_height: viewportHeight ?? 600,
        environment: 'browser',
      },
    ],
    instructions: prompt,
    input: [
      {
        role: 'user',
        content: [
          {
            type: 'input_image',
            detail: 'low',
            image_url: `data:image/jpeg;base64,${screenshot}`,
          },
        ],
      },
    ],
  });
  const end = Date.now();
  const duration = end - start;

  return {
    callDuration: duration,
    result: response,
  };
}
