import { PageStateResult } from '../agent/types/extract-result';
import { githubLoginForm, verifyDeviceForm } from './forms';

export const mock0: PageStateResult = {
  formTitle: 'Welcome Back',
  formDescription: 'Please sign in to your account',
  errors: [],
  pageType: 'credentials',
  htmxForm:
    '<form class="form-container" hx-ws="send">\n  <h2 class="form-title">Welcome Back</h2>\n  <p class="form-description">Please sign in to your account</p>\n  <div class="input-container">\n    <label class="form-label" for="login">Email Address</label>\n    <input type="text" class="input-field" id="login" name="login" placeholder="Enter your email" aria-label="Email Address" required>\n  </div>\n  <div class="input-container">\n    <label class="form-label" for="password">Password</label>\n    <input type="password" class="input-field" id="password" name="password" placeholder="Enter your password" aria-label="Password" required>\n  </div>\n  <div class="button-container">\n    <button type="submit" class="button-primary">Sign In</button>\n  </div>\n</form>',
  actions: [
    {
      type: 'fill',
      name: 'login',
      value: '',
      coordinates: {
        x: 509,
        y: 397,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'fill',
      name: 'password',
      value: '',
      coordinates: {
        x: 509,
        y: 498,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'Sign In',
      value: '',
      coordinates: {
        x: 619,
        y: 591,
      },
      order: 2,
      isSubmitAction: true,
    },
  ],
};
export const mock1: PageStateResult = {
  formTitle: 'Security Check',
  formDescription: 'Verifying for: <EMAIL>',
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form hx-ws="send" class="form-container" aria-labelledby="form-title" aria-describedby="form-description">\n  <h1 id="form-title" class="form-title">Security Check</h1>\n  <p id="form-description" class="form-description">Verifying for: <EMAIL></p>\n  <div class="input-container">\n    <label for="captcha" class="form-label">Enter Captcha</label>\n    <input type="text" id="captcha" name="captcha" class="input-field" aria-label="Type the captcha text" placeholder="Type the text above">\n  </div>\n  <div class="button-container">\n    <button type="submit" class="button-primary">Verify</button>\n  </div>\n</form>',
  actions: [
    {
      type: 'fill',
      name: 'captcha',
      value: '',
      coordinates: {
        x: 517,
        y: 582,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'Verify',
      coordinates: {
        x: 603,
        y: 680,
      },
      order: 1,
      isSubmitAction: true,
    },
  ],
};
export const mock: PageStateResult = {
  formTitle: '2-Step Verification',
  formDescription:
    "To help keep your account safe, Google wants to make sure it's really you trying to sign in. Open the Gmail app on your iPhone and tap Yes on the prompt to verify it's you.",
  errors: [],
  pageType: '2-factor-auth',
  htmxForm:
    '<form class="form-container" hx-ws="send">\n    <h1 class="form-title">2-Step Verification</h1>\n    <p class="form-description">To help keep your account safe, Google wants to make sure it\'s really you trying to sign in. Open the Gmail app on your iPhone and tap Yes on the prompt to verify it\'s you.</p>\n    <div class="input-container">\n        <label for="account" class="form-label">Email</label>\n        <select id="account" name="account" class="select-field">\n            <option selected><EMAIL></option>\n        </select>\n    </div>\n    <div class="checkbox-container">\n        <input type="checkbox" id="rememberDevice" name="rememberDevice" class="checkbox-field" checked>\n        <label for="rememberDevice" class="checkbox-label">Don\'t ask again on this device</label>\n    </div>\n    <div class="button-container">\n        <button type="button" class="button-primary">Verify</button>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'rememberDevice',
      coordinates: {
        x: 437,
        y: 237,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'useOtherMethod',
      coordinates: {
        x: 721,
        y: 416,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'verify',
      coordinates: {
        x: 652,
        y: 443,
      },
      order: 2,
      isSubmitAction: true,
    },
  ],
};
export const mockCallAfterCaptchaClicked: PageStateResult = {
  formTitle: 'Select images',
  formDescription: 'Select all images with a fire hydrant. Click verify once there are none left.',
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form hx-ws="send" class="form-container">\n  <div class="form-description">\n    Select all images with a fire hydrant. Click verify once there are none left.\n  </div>\n  <div class="captcha-container">\n    <div class="image-grid">\n      <img src="/path/to/image" alt="Image 1" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 2" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 3" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 4" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 5" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 6" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 7" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 8" class="captcha-image"/>\n      <img src="/path/to/image" alt="Image 9" class="captcha-image"/>\n    </div>\n  </div>\n  <div class="button-container">\n    <button type="submit" class="button-primary">Verify</button>\n  </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'image1',
      value: '',
      coordinates: {
        x: 379,
        y: 205,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'image3',
      value: '',
      coordinates: {
        x: 500,
        y: 205,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'image7',
      value: '',
      coordinates: {
        x: 381,
        y: 465,
      },
      order: 2,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'verify',
      value: '',
      coordinates: {
        x: 644,
        y: 563,
      },
      order: 3,
      isSubmitAction: true,
    },
  ],
};

export const initialMock: PageStateResult = {
  formTitle: 'reCAPTCHA V2 Callback demo',
  formDescription:
    "This page explains how reCAPTCHA V2 Callback is displayed and how reCAPTCHA V2 Callback verification works. Sometimes there's no submit button and a callback function is used instead. The function is executed when reCAPTCHA is solved.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form class="form-container" hx-ws="send" novalidate>\n    <div class="form-section">\n        <div class="input-container" aria-label="CAPTCHA">\n            <div class="g-recaptcha" data-sitekey="your_site_key"></div>\n        </div>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'captcha',
      value: '',
      coordinates: {
        x: 273,
        y: 329,
      },
      order: 0,
      isSubmitAction: true,
    },
  ],
};

export const screen1920Later: PageStateResult = {
  formTitle: 'reCAPTCHA V2 Callback',
  formDescription: 'Solve the CAPTCHA by selecting all images with cars and click VERIFY.',
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form hx-ws="send" class="form-container">\n    <div class="form-label" aria-label="CAPTCHA Instructions">Select all images with cars. Click VERIFY once there are none left.</div>\n    <div class="input-container">\n        <div class="captcha-container">\n            <!-- Placeholder for CAPTCHA images -->\n        </div>\n    </div>\n    <div class="button-container">\n        <button type="submit" class="button-primary" aria-label="Verify">VERIFY</button>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'captcha_image',
      value: 'car',
      coordinates: {
        x: 790,
        y: 206,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'captcha_image',
      value: 'car',
      coordinates: {
        x: 1021,
        y: 535,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'verify_button',
      value: '',
      coordinates: {
        x: 1067,
        y: 579,
      },
      order: 2,
      isSubmitAction: true,
    },
  ],
};

export const screen1920Initial: PageStateResult = {
  formTitle: 'reCAPTCHA V2 Callback demo',
  formDescription:
    'This page demonstrates how the reCAPTCHA V2 Callback works without a submit button but using a callback function after verification.',
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form hx-ws="send" class="form-container">\n    <div class="input-container">\n        <div class="g-recaptcha" data-sitekey="YOUR_SITE_KEY"></div>\n    </div>\n    <div class="button-container">\n        <button type="submit" class="button-primary">Verify</button>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'reCAPTCHA checkbox',
      value: '',
      coordinates: {
        x: 747,
        y: 328,
      },
      order: 0,
      isSubmitAction: true,
    },
  ],
};

export const initialDataOn1920x1024: PageStateResult = {
  formTitle: 'reCAPTCHA V2 Callback demo',
  formDescription:
    "This page explains how reCAPTCHA V2 Callback is displayed and how reCAPTCHA V2 Callback verification works. Sometimes there's no submit button and a callback function is used instead. The function is executed when reCAPTCHA is solved.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<div class="form-container">\n    <h2 class="form-title">reCAPTCHA V2 Callback demo</h2>\n    <p class="form-description">This page explains how reCAPTCHA V2 Callback is displayed and how reCAPTCHA V2 Callback verification works. Sometimes there\'s no submit button and a callback function is used instead. The function is executed when reCAPTCHA is solved.</p>\n    <div class="input-container" style="margin-top: 20px;">\n        <div class="g-recaptcha" data-sitekey="your_site_key" aria-label="CAPTCHA"></div>\n    </div>\n    <!-- Normally a submit button would be placed here if there was a form to submit -->\n</div>',
  actions: [
    {
      type: 'click',
      name: '',
      value: '',
      coordinates: {
        x: 723,
        y: 315,
      },
      order: 0,
      isSubmitAction: true,
    },
  ],
};

export const dataAfterCaptchaClickedOn1920x1024: PageStateResult = {
  formTitle: 'reCAPTCHA Verification',
  formDescription:
    "Please complete the reCAPTCHA verification by selecting all images with crosswalks, then click 'Verify'.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form class="form-container" hx-ws="send" aria-label="reCAPTCHA Verification">\n    <div class="form-section" aria-label="Image Selection">\n        <div class="form-section-title">Select all images with crosswalks</div>\n        <div class="captcha-container" aria-label="Image Grid">\n            <!-- Images would be inserted here dynamically -->\n        </div>\n    </div>\n    <div class="button-container">\n        <button type="submit" class="button-primary" aria-label="Verify">Verify</button>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'image_selection',
      value: '',
      coordinates: {
        x: 886,
        y: 355,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'image_selection',
      value: '',
      coordinates: {
        x: 1039,
        y: 369,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'image_selection',
      value: '',
      coordinates: {
        x: 814,
        y: 496,
      },
      order: 2,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'image_selection',
      value: '',
      coordinates: {
        x: 1050,
        y: 549,
      },
      order: 3,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'verify',
      value: '',
      coordinates: {
        x: 1066,
        y: 586,
      },
      order: 4,
      isSubmitAction: true,
    },
  ],
};

export const loginPageState: PageStateResult = {
  formTitle: 'Facebook Login',
  formDescription: 'Facebook helps you connect and share with the people in your life.',
  errors: [],
  pageType: 'credentials',
  htmxForm:
    '<div class="form-container">\n  <h1 class="form-title">Facebook Login</h1>\n  <p class="form-description">Facebook helps you connect and share with the people in your life.</p>\n  <form  hx-ws="send">\n    <div class="input-container">\n      <label for="email" class="form-label">Email address or phone number</label>\n      <input type="text" id="email" name="email" class="input-field" aria-label="Email address or phone number" required />\n    </div>\n    <div class="input-container">\n      <label for="password" class="form-label">Password</label>\n      <input type="password" id="password" name="password" class="input-field" aria-label="Password" required />\n    </div>\n    <div class="button-container">\n      <button type="submit" class="button-primary">Log in</button>\n    </div>\n  </form>\n</div>',
  actions: [
    {
      type: 'fill',
      name: 'email',
      value: '',
      coordinates: {
        x: 436,
        y: 250,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'fill',
      name: 'password',
      value: '',
      coordinates: {
        x: 436,
        y: 318,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'login',
      value: '',
      coordinates: {
        x: 395,
        y: 380,
      },
      order: 2,
      isSubmitAction: true,
    },
  ],
};

//Simulated Login
export const testLoginInitialPage: PageStateResult = {
  formTitle: 'Welcome Back',
  formDescription: 'Please sign in to your account',
  errors: [],
  pageType: 'credentials',
  htmxForm:
    '<form class="form-container" hx-ws="send" aria-labelledby="formTitle">\n  <h2 id="formTitle" class="form-title">Welcome Back</h2>\n  <p class="form-description">Please sign in to your account</p>\n  <div class="input-container">\n    <label for="login" class="form-label">Email Address</label>\n    <input type="email" id="login" name="login" class="input-field" placeholder="Enter your email" required />\n  </div>\n  <div class="input-container">\n    <label for="password" class="form-label">Password</label>\n    <input type="password" id="password" name="password" class="input-field" placeholder="Enter your password" required />\n  </div>\n  <div class="button-container">\n    <button type="submit" class="button-primary">Sign In</button>\n  </div>\n</form>',
  actions: [
    {
      type: 'fill',
      name: 'login',
      value: '',
      coordinates: {
        x: 375,
        y: 245,
      },
      order: 0,
      isSubmitAction: false,
    },
    {
      type: 'fill',
      name: 'password',
      value: '',
      coordinates: {
        x: 375,
        y: 345,
      },
      order: 1,
      isSubmitAction: false,
    },
    {
      type: 'click',
      name: 'Sign In',
      coordinates: {
        x: 400,
        y: 455,
      },
      order: 2,
      isSubmitAction: true,
    },
  ],
};

export const testLoginCaptcha1: PageStateResult = {
  formTitle: 'Security Check',
  formDescription: "Please verify you're not a robot by solving the CAPTCHA.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form class="form-container" hx-ws="send" aria-labelledby="form-title">\n    <h1 id="form-title" class="form-title">Security Check</h1>\n    <p class="form-description">Please verify you\'re not a robot by solving the CAPTCHA.</p>\n    <div class="input-container">\n        <div class="g-recaptcha" data-sitekey="YOUR_SITE_KEY"></div>\n    </div>\n    <div class="input-container">\n        <label for="captcha-input" class="form-label">Type the word you see below:</label>\n        <input type="text" id="captcha-input" name="captcha-input" class="input-field" aria-label="CAPTCHA Input">\n    </div>\n    <div class="button-container">\n        <button type="submit" class="button-primary">Verify</button>\n    </div>\n</form>',
  actions: [
    {
      type: 'click',
      name: 'recaptcha-checkbox',
      value: '',
      coordinates: {
        x: 279,
        y: 357,
      },
      order: 0,
      isSubmitAction: false,
    },
  ],
};

export const testLoginCaptcha2: PageStateResult = {
  formTitle: 'Security Check',
  formDescription:
    "Please confirm you're not a robot by clicking the checkbox and following the on-screen instructions.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form class="form-container" aria-label="Security Check">\n    <div class="form-section">\n        <div class="input-container">\n            <div class="checkbox-container" aria-label="captcha-checkbox">\n                <input type="checkbox" id="captcha-checkbox" class="captcha-field" />\n                <label for="captcha-checkbox" class="captcha-label">I\'m not a robot</label>\n            </div>\n        </div>\n        <div class="button-container">\n            <button type="button" class="button-primary">Verify</button>\n        </div>\n    </div>\n</form>\n',
  actions: [
    {
      type: 'click',
      name: 'captcha-checkbox',
      value: '',
      coordinates: {
        x: 275,
        y: 290,
      },
      order: 0,
      isSubmitAction: false,
    },
  ],
};

export const captchaWithHiddenInput: PageStateResult = {
  formTitle: 'reCAPTCHA V2 Callback demo',
  formDescription:
    "This page explains how reCAPTCHA V2 Callback is displayed and how reCAPTCHA V2 Callback verification works. Sometimes there's no submit button and a callback function is used instead. The function is executed when reCAPTCHA is solved.",
  errors: [],
  pageType: 'captcha',
  htmxForm:
    '<form class="form-container" hx-ws="send" novalidate>\n' +
    '    <div class="form-section">\n' +
    '        <div class="input-container" aria-label="CAPTCHA">\n' +
    '            <div class="g-recaptcha" data-sitekey="your_site_key"></div>\n' +
    '            <input id="text-captcha-input" type="hidden" autofocus/>\n' +
    '        </div>\n' +
    '    </div>\n' +
    '</form>\n' +
    '<script>\n' +
    "  const input = document.getElementById('text-captcha-input');\nconsole.log('Kelly lalalalala');\n" +
    '\n' +
    "  input.addEventListener('input', (event) => {\n" +
    '    const value = event.target.value;\n' +
    '    const lastChar = value.charAt(value.length - 1);\n' +
    "    console.log('Character typed:', lastChar);\n" +
    '  });\n' +
    '</script>',
  actions: [
    {
      type: 'click',
      name: 'captcha',
      value: '',
      coordinates: {
        x: 273,
        y: 329,
      },
      order: 0,
      isSubmitAction: true,
    },
  ],
};

//export const textCaptcha: PageStateResult =
