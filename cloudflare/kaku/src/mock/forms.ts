import {PageStateResult} from "../agent/types/extract-result";

export const githubLoginForm: PageStateResult = {
    "formTitle": "Sign in to GitHub",
    "formDescription": "",
    "errors": [],
    "pageType": "credentials",
    "htmxForm": "<form hx-ws=\"send\" class=\"form-container\">\n  <h1 class=\"form-title\">Sign in to GitHub</h1>\n  <div class=\"input-container\">\n    <label class=\"form-label\" for=\"login\">Username or email address</label>\n    <input class=\"input-field\" type=\"text\" id=\"login\" name=\"login\" aria-label=\"Username or email address\" required>\n  </div>\n  <div class=\"input-container\">\n    <label class=\"form-label\" for=\"password\">Password</label>\n    <input class=\"input-field\" type=\"password\" id=\"password\" name=\"password\" aria-label=\"Password\" required>\n  </div>\n  <div class=\"button-container\">\n    <button type=\"submit\" class=\"button-primary\">Sign in</button>\n  </div>\n</form>",
    "actions": [
        {
            "type": "fill",
            "name": "login",
            "value": "",
            "coordinates": {
                "x": 378,
                "y": 220
            },
            "order": 0,
            "isSubmitAction": false
        },
        {
            "type": "fill",
            "name": "password",
            "value": "",
            "coordinates": {
                "x": 378,
                "y": 298
            },
            "order": 1,
            "isSubmitAction": false
        },
        {
            "type": "click",
            "name": "Sign in",
            "value": "",
            "coordinates": {
                "x": 395,
                "y": 345
            },
            "order": 2,
            "isSubmitAction": true
        }
    ]
}

export const verifyDeviceForm: PageStateResult = {
    "formTitle": "Device verification",
    "formDescription": "Please enter the authentication code sent to your email to verify your device. The code will expire at 3:27PM EAT.",
    "errors": [],
    "pageType": "otp",
    "htmxForm": "<div class=\"form-container\" hx-ws=\"send\">\n    <h1 class=\"form-title\">Device verification</h1>\n    <div class=\"form-section\">\n        <div class=\"form-section-title\">Email</div>\n        <p>We just sent your authentication code via email to k************@gmail.com. The code will expire at 3:27PM EAT.</p>\n        <div class=\"input-container\">\n            <label for=\"otp\" class=\"form-label\">Device Verification Code</label>\n            <input type=\"text\" name=\"otp\" id=\"otp\" class=\"input-field\" aria-label=\"Device Verification Code\" placeholder=\"XXXXXX\">\n        </div>\n        <div class=\"button-container\">\n            <button type=\"submit\" class=\"button-primary\">Verify</button>\n        </div>\n    </div>\n</div>",
    "actions": [
        {
            "type": "fill",
            "name": "otp",
            "value": "",
            "coordinates": {
                "x": 392,
                "y": 419
            },
            "order": 0,
            "isSubmitAction": false
        },
        {
            "type": "click",
            "name": "Verify",
            "value": "",
            "coordinates": {
                "x": 392,
                "y": 465
            },
            "order": 1,
            "isSubmitAction": true
        }
    ]
}

export const authenticatedForm: PageStateResult = {
    "formTitle": "",
    "formDescription": "",
    "errors": [],
    "pageType": "authenticated",
    "htmxForm": "",
    "actions": []
}