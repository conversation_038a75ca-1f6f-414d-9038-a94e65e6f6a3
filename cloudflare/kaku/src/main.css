@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

  form {
    @apply w-full px-6;
  }

  /* Form Layout */
  .form-container {
    @apply flex flex-col w-full rounded-lg items-center;
  }

  /* Typography */
  .form-title {
    @apply text-base font-bold text-center text-[#49454E] px-6 py-1;
  }

  .form-description {
    @apply text-base text-gray-600 text-start mb-6 mt-1 px-6;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-600 mb-2;
  }

  .form-error {
    @apply text-sm text-red-500 my-2 py-1;
  }

  .form-help-text {
    @apply text-xs text-gray-400 mt-1;
  }

  /* Input Fields */
  .input-container {
    @apply inline-flex flex-col space-y-1 w-full mb-4 justify-center items-start;
  }

  .input-field {
    @apply w-full px-4 py-2 bg-transparent border border-gray-600 rounded-[4px] text-black focus:outline-none focus:border-primary-700 transition duration-200;

  }

  .input-field-error {
    @apply w-full px-4 py-2 bg-gray-700 border border-red-500 rounded-lg text-white focus:outline-none focus:border-red-400;
  }

  /* Checkbox & Radio */
  .checkbox-container {
    @apply flex items-center space-x-2 mb-4;
  }

  .checkbox-label {
    @apply text-sm text-gray-300;
  }

  .checkbox-field {
    @apply h-4 w-4 rounded border-gray-600 text-primary-600 focus:ring-primary-500 bg-gray-700;
  }

  .radio-container {
    @apply flex flex-col space-y-2 mb-4;
  }

  .radio-option {
    @apply flex items-center space-x-2;
  }

  .radio-field {
    @apply h-4 w-4 border-gray-600 text-primary-600 focus:ring-primary-500 bg-gray-700;
  }

  /* Select */
  .select-container {
    @apply flex flex-col space-y-1 w-full mb-4;
  }

  .select-field {
    @apply w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500 appearance-none;
  }

  /* Buttons */
  .button-container {
    @apply flex space-x-3 w-full mt-3;
  }

  .button-primary {
    @apply w-full py-2 px-4 bg-primary-700 hover:bg-primary-800 text-white rounded-lg transition duration-200;
  }

  .button-secondary {
    @apply w-full py-2 px-4 border border-gray-600 bg-gray-700 hover:bg-gray-600 text-white font-semibold rounded-lg transition duration-200;
  }

  .button-danger {
    @apply w-full py-2 px-4 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-lg transition duration-200;
  }

  /* OTP Input */
  .otp-container {
    @apply flex justify-between space-x-2 w-full mb-4;
  }

  .otp-input {
    @apply w-12 h-12 text-center bg-gray-700 border border-gray-600 rounded-lg text-white text-xl focus:outline-none focus:border-primary-500;
  }

  /* 2FA and Security Elements */
  .security-badge {
    @apply inline-flex items-center space-x-1 px-2 py-1 bg-gray-700 rounded text-xs text-gray-300 mb-4;
  }

  .security-icon {
    @apply w-4 h-4 text-primary-500;
  }

  /* Loading States */
  .loading-spinner {
    @apply animate-spin h-5 w-5 text-white;
  }

  .button-loading {
    @apply opacity-75 cursor-not-allowed;
  }

  /* Captcha Container */
  .captcha-container {
    @apply w-full bg-gray-700 border border-gray-600 rounded-lg p-4 mb-4 flex justify-center items-center;
  }

  /* Form Sections */
  .form-section {
    @apply border-t border-gray-700 pt-4 mt-4;
  }

  .form-section-title {
    @apply text-lg font-medium text-white mb-4;
  }

  /* Form Footer */
  .form-footer {
    @apply text-xs text-gray-400 text-center mt-6;
  }

  /* Link Styles */
  .form-link {
    @apply text-primary-400 hover:text-primary-300 underline;
  }
}