/**
 * Template engine for the UI using Hono HTML helper
 */
import { html, raw } from 'hono/html';
import { AgentState } from '../agent/types';
import { LiveView, LLMFormContainer, Loading, ServiceContext } from './components';
import { ErrorDisplay } from './components/error-display';
import { ProcessedError } from '../common/error/types';

import { platformDetails, PlatformTypes } from './constants';
import { HTMLReturnType } from './types';

export interface TemplateContext extends ServiceContext {}
export class TemplateEngine {
  /** Render LLM-generated form */
  renderLLMForm(formContent: string, context: TemplateContext): HTMLReturnType {
    return LLMFormContainer({ ...context, formContent });
  }

  /** Render live view */
  renderLiveView(context: TemplateContext): HTMLReturnType {
    return LiveView(context);
  }

  /** Render loading indicator */
  renderLoading(loadingText?: string): HTMLReturnType {
    return Loading({ loadingText });
  }

  /** <PERSON><PERSON> completed state */
  renderCompleted() {
    return html`
      <div
        class="flex flex-col items-center p-6 bg-green-100 border border-green-300 rounded-lg shadow-lg max-w-md mx-[10px]"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-12 w-12 text-green-600 mb-3"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
        <h2 class="text-lg font-semibold text-green-700">Authentication Complete!</h2>
        <p class="text-sm text-green-800 text-center mb-4">
          Thank you for trusting us with your session! We&apos;ll never share your information with anyone.
        </p>

          <div class="w-full space-y-3 mb-4">
            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
              <h3 class="text-sm font-medium text-green-700 mb-1">
                What&apos;s Next?
              </h3>
              <p class="text-xs text-green-600">
                Your session is secure and will help you start earning passively through our automation tools.
              </p>
            </div>
          </div>

          <button
            id="demo-session-btn"
            class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
            onclick="demonstrateSession()"
          >
            Test Stored Session
          </button>

          <div id="demo-result" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p class="text-sm text-blue-800" id="demo-message">Testing session...</p>
          </div>
        </div>

        <script>
          async function demonstrateSession() {
            const btn = document.getElementById('demo-session-btn');
            const resultDiv = document.getElementById('demo-result');
            const messageEl = document.getElementById('demo-message');

            btn.disabled = true;
            btn.textContent = '⏳ Testing...';
            resultDiv.classList.remove('hidden');
            messageEl.textContent = 'Retrieving stored session and testing authentication...';

            try {
              // Get current URL to extract userId and platformId
              const pathParts = window.location.pathname.split('/');
              const userId = pathParts[pathParts.length - 2];
              const platformId = pathParts[pathParts.length - 1];

              const response = await fetch(\`/demo-session/\${userId}/\${platformId}\`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
              });

              const result = await response.json();

              if (result.success) {
                messageEl.innerHTML = \`
                  <strong>✅ Session Test Successful!</strong><br>
                  <span class="text-xs">
                    • Retrieved stored session data<br>
                    • Created new browser instance<br>
                    • Loaded session cookies and storage<br>
                    • Verified authentication persistence
                  </span>
                \`;
                resultDiv.className = 'bg-green-50 border border-green-200 rounded-lg p-3';
              } else {
                messageEl.innerHTML = \`
                  <strong>❌ Session Test Failed</strong><br>
                  <span class="text-xs">\${result.error || 'Unknown error occurred'}</span>
                \`;
                resultDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-3';
              }
            } catch (error) {
              messageEl.innerHTML = \`
                <strong>❌ Test Error</strong><br>
                <span class="text-xs">Failed to connect to demonstration service</span>
              \`;
              resultDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-3';
            }

            btn.disabled = false;
            btn.textContent = '🔄 Test Again';
          }
        </script>
      </div>
    `;
  }

  renderTermsAndConditions(platform: string): HTMLReturnType {
    const platformInfo = platformDetails[platform];

    if (!platformInfo) {
      return this.renderError('Platform not supported');
    }

    return html`<div class="p-6">
      <div class="flex flex-row items-center justify-center">
        <img src="/kazeel-logo.png" alt="Kazeel Logo" class="w-16 h-16 mt-4 mb-2" />
        <img
          src="${platformInfo.logo}"
          alt="Facebook Logo"
          class="w-16 h-16 mt-4 mb-2 z-[1] -m-4"
        />
      </div>

      <div class="p-2 text-center">
        <h2 class="text-xl font-semibold my-4">
          Kazeel will connect you with ${platformInfo.name}
        </h2>
      </div>
      <div class="p-2">
        <div class="space-y-4">
          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                width="13"
                height="15"
                viewBox="0 0 13 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                class="size-6"
              >
                <path
                  d="M5.43717 9.6263H7.56217L7.15488 7.34193C7.39099 7.22387 7.57693 7.05269 7.7127 6.82839C7.84846 6.60408 7.91634 6.35616 7.91634 6.08464C7.91634 5.69505 7.77763 5.36155 7.5002 5.08411C7.22276 4.80668 6.88926 4.66797 6.49967 4.66797C6.11009 4.66797 5.77658 4.80668 5.49915 5.08411C5.22172 5.36155 5.08301 5.69505 5.08301 6.08464C5.08301 6.35616 5.15089 6.60408 5.28665 6.82839C5.42242 7.05269 5.60836 7.22387 5.84447 7.34193L5.43717 9.6263ZM6.49967 14.5846C4.8587 14.1714 3.50401 13.2299 2.43561 11.7602C1.36721 10.2904 0.833008 8.65825 0.833008 6.8638V2.54297L6.49967 0.417969L12.1663 2.54297V6.8638C12.1663 8.65825 11.6321 10.2904 10.5637 11.7602C9.49533 13.2299 8.14065 14.1714 6.49967 14.5846ZM6.49967 13.0971C7.72745 12.7076 8.74273 11.9284 9.54551 10.7596C10.3483 9.59089 10.7497 8.29227 10.7497 6.8638V3.51693L6.49967 1.92318L2.24967 3.51693V6.8638C2.24967 8.29227 2.65106 9.59089 3.45384 10.7596C4.25662 11.9284 5.2719 12.7076 6.49967 13.0971Z"
                  fill="#1C1B1F"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Provide your credentials</h3>
              <p class="text-gray-600 text-xs">We'll guide you step by step as you sign in</p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Real-Time Transparency</h3>
              <p class="text-gray-600 text-xs">
                Use "Live View" to watch our secure connection process as it happens
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M15 12H9m12 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Disable Anytime</h3>
              <p class="text-gray-600 text-xs">
                You're always in control—turn off this connection in your Kazeel settings anytime
              </p>
            </div>
          </div>

          <div class="flex items-start">
            <div class="bg-[#ECE7EB] p-2 rounded-full mr-3 flex-shrink-0">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke-width="1.5"
                stroke="currentColor"
                class="size-6"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  d="M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"
                />
              </svg>
            </div>
            <div>
              <h3 class="font-medium text-[#49454E] text-sm">Protecting Your Privacy</h3>
              <p class="text-gray-600 text-xs">
                We securely store your authentication and protect your privacy
              </p>
            </div>
          </div>
        </div>
        <p class="my-6 text-xs text-gray-500 text-center">
          <strong>Disclaimer:</strong> Kazeel is an independent application and is not affiliated
          with, endorsed by, or sponsored by any third parties or their trademarks, logos, or
          copyrights.
        </p>
      </div>
      <div class="flex flex-col space-y-3">
        <button
          class="bg-primary-900 text-white px-4 py-2 rounded-lg hover:bg-primary-700 w-full dark:bg-primary-700 dark:hover:bg-primary-600"
          hx-ws="send"
          hx-vals='{"type": "agree_and_continue"}'
        >
          Agree and Continue
        </button>
        <button
          class="border border-gray-400 text-gray-700 px-4 py-2 rounded-lg w-full dark:border-primary-900 dark:text-primary-900 dark:hover:bg-gray-700 font-medium"
        >
          Decline
        </button>
      </div>
      <div class="mt-14 flex items-center justify-center">
        <a href="asda" class="text-xs text-[#7A757F] underline">Privacy Policy</a>
        <span class="text-xs text-[#7A757F] mx-[2px]">•</span>
        <a href="asda" class="text-xs text-[#7A757F] underline">English (United States)</a>
      </div>
    </div>`;
  }

  /** Render error state */
  renderError(message: string): HTMLReturnType {
    return html` <div class="text-center p-4 bg-red-200 rounded-lg">Error: ${message}</div> `;
  }

  /** Render complex error state */
  renderComplexError(agentState: AgentState): HTMLReturnType {
    const errorData: ProcessedError = {
      userMessage: agentState.errorMessage || 'An error occurred',
      shouldReplaceCard: true,
      logLevel: 'error',
      errorCode: 'SYSTEM_ERROR',
    };
    return ErrorDisplay(errorData);
  }

  /** Render waiting-for-human form */
  renderWaitingForHumanForm(agentState: AgentState): HTMLReturnType {
    if (agentState.interactivity?.status === 'enabled') {
      const captchaDescription =
        agentState.page?.formDescription || 'Please complete the captcha to continue.';
      const captchaUI = html`
        <div id="captcha-interaction" hx-swap-oob="innerHTML">
          <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4" role="alert">
            <p class="font-bold">${agentState.page?.formTitle || 'Captcha Detected'}</p>
            <p>${captchaDescription}</p>
            <p class="mt-2 text-sm text-gray-600">
              Please solve the captcha. The system will automatically detect changes.
            </p>
          </div>
          <div id="captcha-status" class="mt-4">
            <!-- Status -->
          </div>
        </div>
      `;
      return captchaUI;
    }
    if (agentState.interactivity?.status === 'paused') {
      const updateUI = html`
        <div id="captcha-status" hx-swap-oob="innerHTML">
          <div class="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4" role="alert">
            <p class="font-bold">Change Detected</p>
            <p>Changes detected in the captcha. Verifying with LLM...</p>
          </div>
        </div>
      `;
      return updateUI;
    }
    return html`<div
      id="form-container"
      hx-swap-oob="innerHTML"
      class="w-full h-screen md:h-screen flex flex-col justify-evenly items-center"
    >
      <img
        id="live-view-img"
        src="https://ishadeed.com/assets/fb-login/fb-new-login.png"
        alt="Facebook Login Page"
        class="h-auto min-h-60 hidden"
      />

      ${raw(agentState.page?.htmxForm)}

      <div class="flex flex-col items-center p-9 ">
        <p class="form-description text-center mt-6 text-xs mb-1">
          Curious how it works? Tap below to watch the process.
        </p>
        <button
          id="toggle-live-view"
          class="text-primary-700 hover:text-blue-800 font-medium text-sm hidden"
          onclick="toggleLiveView()"
        >
          Show Live View
        </button>
      </div>

      <script>
        function toggleLiveView() {
          const img = document.getElementById('live-view-img');
          const button = document.getElementById('toggle-live-view');

          if (img.classList.contains('hidden')) {
            img.classList.remove('hidden');
            button.textContent = 'Hide Live View';
          } else {
            img.classList.add('hidden');
            button.textContent = 'Show Live View';
          }
        }
      </script>
    </div>`;
  }

  generateContent(agentState: AgentState, platform: PlatformTypes) {
    console.log(`Generating content for status ${agentState.status}`);
    switch (agentState.status) {
      case 'waiting-for-agent':
        return this.renderLoading();
      case 'waiting-for-human':
        return this.renderWaitingForHumanForm(agentState);
      case 'completed':
        return this.renderCompleted();
      case 'initiated':
        return this.renderLoading();
      case 'other':
        return this.renderLoading();
      case 'initial':
        return this.renderTermsAndConditions(platform);
      case 'error':
        return this.renderComplexError(agentState);
    }
  }
}

export const templateEngine = new TemplateEngine();
