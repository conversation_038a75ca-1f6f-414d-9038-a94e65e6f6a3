import { html } from 'hono/html';
import { ProcessedError } from '../../common/error/types';

export const CriticalError = (error: ProcessedError) => html`
  <div class="h-screen w-full flex flex-col items-center justify-center bg-white text-black md:h-[750px] md:w-[435px] md:rounded-xl">
    <div class="flex flex-col items-center p-6 max-w-md w-full">
      <!-- Error Icon -->
      <div class="flex justify-center mb-6">
        <div class="w-32 h-32 bg-red-100 rounded-full flex items-center justify-center border-2 border-red-300">
          <div class="text-red-600 font-bold" style="font-size: 3rem; line-height: 1;">⚠</div>
        </div>
      </div>

      <!-- Error Message with custom styling -->
      <h2 class="text-base font-bold text-center mb-3" style="color: #49454E;">Connection Issue</h2>
      <div class="text-base text-gray-600 text-center mb-6 px-6">
        ${error.userMessage}
      </div>

      <!-- Additional Context -->
      <p class="text-xs text-gray-400 text-center mb-6 px-6">
        We're working to resolve this issue. If it persists, please contact our support team.
      </p>

      <!-- Action Button using purple primary button -->
      <div class="w-full px-6 mb-4">
        <button 
          class="button-primary"
          onclick="
            this.textContent = 'Retrying...';
            this.disabled = true;
            
            const retryEvent = new CustomEvent('retry-request');
            document.dispatchEvent(retryEvent);
            
            window.location.reload();
          "
        >
          Try Again
        </button>
      </div>

      <!-- Support Link -->
      <div class="text-center border-t border-gray-200 pt-4 w-full">
        <a href="#" class="form-link text-xs">
          Contact Support
        </a>
      </div>
    </div>
  </div>
`;

export const WarningBanner = (error: ProcessedError, dismissible: boolean = true) => html`
  <div id="warning-banner" class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4 mx-6 rounded-lg shadow-sm">
    <div class="flex items-start">
      <div class="flex-shrink-0">
        <div class="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </div>
      </div>
      <div class="ml-3 flex-1">
        <p class="text-sm text-yellow-800 font-medium">
          ${error.userMessage}
        </p>
      </div>
      ${dismissible ? html`
        <div class="ml-auto pl-3">
          <button 
            class="text-yellow-600 hover:text-yellow-800 transition-colors duration-200 p-1 rounded-full hover:bg-yellow-100"
            onclick="document.getElementById('warning-banner').remove()"
          >
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      ` : ''}
    </div>
  </div>
`;

export const InfoNotification = (message: string, autoHide: boolean = true) => html`
  <div 
    id="info-notification" 
    class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4 mx-6 rounded-lg shadow-sm transition-all duration-300 ${autoHide ? 'fade-out' : ''}"
  >
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
      </div>
      <div class="ml-3">
        <p class="text-sm text-blue-800 font-medium">
          ${message}
        </p>
      </div>
    </div>
  </div>

  ${autoHide ? html`
    <script>
      setTimeout(() => {
        const notification = document.getElementById('info-notification');
        if (notification) {
          notification.style.opacity = '0';
          notification.style.transform = 'translateY(-10px)';
          setTimeout(() => notification.remove(), 300);
        }
      }, 5000);
    </script>
  ` : ''}
`;

export const ErrorDisplay = (error: ProcessedError, options?: { dismissible?: boolean; autoHide?: boolean }) => {
  if (error.shouldReplaceCard) {
    return CriticalError(error);
  }

  switch (error.logLevel) {
    case 'warn':
      return WarningBanner(error, options?.dismissible !== false);
    case 'info':
      return InfoNotification(error.userMessage, options?.autoHide !== false);
    case 'error':
    default:
      return CriticalError(error);
  }
}; 