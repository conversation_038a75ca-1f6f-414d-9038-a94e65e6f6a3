import { html } from 'hono/html';
import { ServiceContext } from './types';

export const LayoutWithCard = (
  { wsEndpoint, userId }: { wsEndpoint: string; userId: string },
  props: ServiceContext,
) => {
  return html` <!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <title>Connect to ${props.serviceName} - <PERSON>zeel</title>
        <script src="https://unpkg.com/htmx.org@2.0.4"></script>
        <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
        <link rel="stylesheet" href="/css/styles.css" />
        <style>
          /* Video container styles */
          #video-container {
            border-radius: 0 0 1.25rem 1.25rem;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(130, 81, 230, 0.2);
            border-top: none;
            box-shadow:
              0 32px 64px -12px rgba(130, 81, 230, 0.15),
              0 20px 40px -8px rgba(0, 0, 0, 0.2),
              0 8px 16px -4px rgba(130, 81, 230, 0.1),
              0 0 0 1px rgba(255, 255, 255, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.15),
              inset 0 -1px 0 rgba(130, 81, 230, 0.1);
            backdrop-filter: blur(20px) saturate(1.2);
            -webkit-backdrop-filter: blur(20px) saturate(1.2);
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(130, 81, 230, 0.05) 25%,
              rgba(255, 255, 255, 0.08) 50%,
              rgba(130, 81, 230, 0.03) 75%,
              rgba(255, 255, 255, 0.05) 100%
            );
            position: relative;
          }

          #video-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0 0 1.25rem 1.25rem;
            padding: 2px;
            background: linear-gradient(
              135deg,
              rgba(130, 81, 230, 0.4) 0%,
              rgba(255, 255, 255, 0.2) 25%,
              rgba(130, 81, 230, 0.2) 50%,
              rgba(255, 255, 255, 0.1) 75%,
              rgba(130, 81, 230, 0.3) 100%
            );
            mask:
              linear-gradient(#fff 0 0) content-box,
              linear-gradient(#fff 0 0);
            -webkit-mask:
              linear-gradient(#fff 0 0) content-box,
              linear-gradient(#fff 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
            z-index: -1;
          }

          /* Disable hover transforms when input overlays are active */
          #video-container:hover:not(.input-overlay-active) {
            transform: translateY(-2px);
            box-shadow:
              0 40px 80px -12px rgba(130, 81, 230, 0.2),
              0 24px 48px -8px rgba(0, 0, 0, 0.25),
              0 12px 24px -4px rgba(130, 81, 230, 0.15),
              0 0 0 1px rgba(255, 255, 255, 0.12),
              inset 0 1px 0 rgba(255, 255, 255, 0.2),
              inset 0 -1px 0 rgba(130, 81, 230, 0.15);
          }

          #video-container.input-overlay-active {
            transform: none !important;
          }

          #video-header {
            background: linear-gradient(
              to bottom,
              rgba(130, 81, 230, 0.95) 0%,
              rgba(130, 81, 230, 0.85) 20%,
              rgba(130, 81, 230, 0.65) 40%,
              rgba(130, 81, 230, 0.35) 70%,
              rgba(130, 81, 230, 0.15) 85%,
              rgba(130, 81, 230, 0.05) 95%,
              transparent 100%
            );
            backdrop-filter: blur(12px) saturate(1.3);
            -webkit-backdrop-filter: blur(12px) saturate(1.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 0;
            position: relative;
            overflow: hidden;
          }

          #video-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.1) 25%,
              transparent 50%,
              rgba(130, 81, 230, 0.1) 75%,
              rgba(130, 81, 230, 0.2) 100%
            );
            pointer-events: none;
          }

          #video-header h2 {
            text-shadow:
              0 2px 8px rgba(0, 0, 0, 0.6),
              0 1px 3px rgba(130, 81, 230, 0.4);
            font-weight: 700;
            letter-spacing: -0.025em;
            position: relative;
            z-index: 2;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 1) 0%,
              rgba(255, 255, 255, 0.95) 50%,
              rgba(255, 255, 255, 0.9) 100%
            );
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
          }

          #video-header p {
            text-shadow:
              0 1px 4px rgba(0, 0, 0, 0.6),
              0 1px 2px rgba(130, 81, 230, 0.3);
            opacity: 0.98;
            position: relative;
            z-index: 2;
            font-weight: 500;
            letter-spacing: 0.01em;
          }

          #interactivity-overlay {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(8px) saturate(1.1);
            -webkit-backdrop-filter: blur(8px) saturate(1.1);
            background: linear-gradient(
              135deg,
              rgba(130, 81, 230, 0.1) 0%,
              rgba(255, 255, 255, 0.05) 50%,
              rgba(130, 81, 230, 0.08) 100%
            );
          }

          #remoteVideo {
            object-fit: contain !important;
            border-radius: 1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow:
              0 8px 32px rgba(130, 81, 230, 0.1),
              0 4px 16px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
          }

          #remoteVideo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 1rem;
            background: linear-gradient(
              135deg,
              rgba(130, 81, 230, 0.05) 0%,
              transparent 25%,
              transparent 75%,
              rgba(130, 81, 230, 0.03) 100%
            );
            pointer-events: none;
            z-index: 1;
          }

          .blur-border-unified {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 20;
            pointer-events: none;
            border-radius: 0.75rem;
            padding: 20px;
            background:
              linear-gradient(white, white) padding-box,
              linear-gradient(
                  45deg,
                  rgba(255, 255, 255, 0.8) 0%,
                  rgba(255, 255, 255, 0.6) 25%,
                  rgba(255, 255, 255, 0.4) 50%,
                  rgba(255, 255, 255, 0.2) 75%,
                  transparent 100%
                )
                border-box;
            border: 20px solid transparent;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            mask:
              linear-gradient(white 0 0) padding-box,
              linear-gradient(white 0 0);
            -webkit-mask:
              linear-gradient(white 0 0) padding-box,
              linear-gradient(white 0 0);
            mask-composite: xor;
            -webkit-mask-composite: xor;
          }

          .blur-border-shadow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 20;
            pointer-events: none;
            border-radius: 0.75rem;
            background: transparent;
            box-shadow:
              inset 0 0 0 1px rgba(255, 255, 255, 0.1),
              inset 20px 0 20px -15px rgba(255, 255, 255, 0.8),
              inset -20px 0 20px -15px rgba(255, 255, 255, 0.8),
              inset 0 20px 20px -15px rgba(255, 255, 255, 0.8),
              inset 0 -20px 20px -15px rgba(255, 255, 255, 0.8),
              inset 20px 20px 25px -15px rgba(255, 255, 255, 0.9),
              inset -20px 20px 25px -15px rgba(255, 255, 255, 0.9),
              inset 20px -20px 25px -15px rgba(255, 255, 255, 0.9),
              inset -20px -20px 25px -15px rgba(255, 255, 255, 0.9);
          }

          .blur-border-edges {
            position: absolute;
            z-index: 15;
            pointer-events: none;
            backdrop-filter: blur(10px) saturate(1.2);
            -webkit-backdrop-filter: blur(10px) saturate(1.2);
            transition: all 0.3s ease-in-out;
          }

          .blur-border-top {
            top: 0;
            left: 24px;
            right: 24px;
            height: 24px;
            background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0.2) 0%,
              rgba(255, 255, 255, 0.15) 20%,
              rgba(255, 255, 255, 0.1) 40%,
              rgba(255, 255, 255, 0.08) 60%,
              rgba(255, 255, 255, 0.05) 80%,
              transparent 100%
            );
            /* No border radius to blend with header */
          }

          .blur-border-bottom {
            bottom: 0;
            left: 24px;
            right: 24px;
            height: 24px;
            background: linear-gradient(
              to top,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.3) 15%,
              rgba(255, 255, 255, 0.25) 30%,
              rgba(255, 255, 255, 0.2) 45%,
              rgba(255, 255, 255, 0.15) 60%,
              rgba(255, 255, 255, 0.1) 75%,
              rgba(255, 255, 255, 0.05) 90%,
              transparent 100%
            );
            /* No border radius to blend with corners */
          }

          .blur-border-left {
            top: 24px;
            bottom: 24px;
            left: 0;
            width: 24px;
            background: linear-gradient(
              to right,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.3) 15%,
              rgba(255, 255, 255, 0.25) 30%,
              rgba(255, 255, 255, 0.2) 45%,
              rgba(255, 255, 255, 0.15) 60%,
              rgba(255, 255, 255, 0.1) 75%,
              rgba(255, 255, 255, 0.05) 90%,
              transparent 100%
            );
            border-radius: 0;
          }

          .blur-border-right {
            top: 24px;
            bottom: 24px;
            right: 0;
            width: 24px;
            background: linear-gradient(
              to left,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.3) 15%,
              rgba(255, 255, 255, 0.25) 30%,
              rgba(255, 255, 255, 0.2) 45%,
              rgba(255, 255, 255, 0.15) 60%,
              rgba(255, 255, 255, 0.1) 75%,
              rgba(255, 255, 255, 0.05) 90%,
              transparent 100%
            );
            border-radius: 0;
          }

          /* Corner blur effects - positioned to avoid overlapping */
          .blur-border-corner {
            position: absolute;
            z-index: 20;
            pointer-events: none;
            width: 24px;
            height: 24px;
            backdrop-filter: blur(12px) saturate(1.3);
            -webkit-backdrop-filter: blur(12px) saturate(1.3);
          }

          .blur-border-corner-tl {
            top: 0;
            left: 0;
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(255, 255, 255, 0.1) 30%,
              rgba(255, 255, 255, 0.05) 60%,
              transparent 100%
            );
            border-radius: 0;
            /* No top radius - blends with header */
          }

          .blur-border-corner-tr {
            top: 0;
            right: 0;
            background: linear-gradient(
              225deg,
              rgba(255, 255, 255, 0.15) 0%,
              rgba(255, 255, 255, 0.1) 30%,
              rgba(255, 255, 255, 0.05) 60%,
              transparent 100%
            );
            border-radius: 0;
            /* No top radius - blends with header */
          }

          .blur-border-corner-bl {
            bottom: 0;
            left: 0;
            background: linear-gradient(
              45deg,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.3) 25%,
              rgba(255, 255, 255, 0.2) 50%,
              rgba(255, 255, 255, 0.1) 75%,
              transparent 100%
            );
            border-radius: 0 0 0 1.25rem;
          }

          .blur-border-corner-br {
            bottom: 0;
            right: 0;
            background: linear-gradient(
              315deg,
              rgba(255, 255, 255, 0.4) 0%,
              rgba(255, 255, 255, 0.3) 25%,
              rgba(255, 255, 255, 0.2) 50%,
              rgba(255, 255, 255, 0.1) 75%,
              transparent 100%
            );
            border-radius: 0 0 1.25rem 0;
          }

          .mobile-fullscreen {
            width: 100vw !important;
            max-width: 100vw !important;
            height: 100vh !important;
            max-height: 100vh !important;
            border-radius: 0 !important;
            margin: 0 !important;
          }

          .loading-indicator {
            background: linear-gradient(
              135deg,
              rgba(255, 255, 255, 0.98) 0%,
              rgba(130, 81, 230, 0.05) 50%,
              rgba(255, 255, 255, 0.95) 100%
            );
            backdrop-filter: blur(16px) saturate(1.2);
            -webkit-backdrop-filter: blur(16px) saturate(1.2);
            border: 1px solid rgba(130, 81, 230, 0.2);
            box-shadow:
              0 20px 25px -5px rgba(130, 81, 230, 0.1),
              0 10px 15px -3px rgba(0, 0, 0, 0.1),
              0 4px 6px -2px rgba(0, 0, 0, 0.05),
              inset 0 1px 0 rgba(255, 255, 255, 0.6);
            position: relative;
            overflow: hidden;
          }

          .loading-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(
              90deg,
              transparent 0%,
              rgba(130, 81, 230, 0.1) 50%,
              transparent 100%
            );
          }

          @keyframes shimmer {
            0% {
              left: -100%;
            }
            100% {
              left: 100%;
            }
          }

          /* Responsive adjustments */
          @media (max-width: 768px) {
            #video-container {
              border-radius: 0;
              border: none;
              border-top: none;
              box-shadow: none;
              background: linear-gradient(
                135deg,
                rgba(255, 255, 255, 0.1) 0%,
                rgba(130, 81, 230, 0.03) 50%,
                rgba(255, 255, 255, 0.05) 100%
              );
            }

            #video-container::before {
              display: none;
            }

            #video-container:hover {
              transform: none;
              box-shadow: none;
            }

            #video-header {
              padding: 1rem;
              border-radius: 0;
              background: linear-gradient(
                to bottom,
                rgba(130, 81, 230, 0.9) 0%,
                rgba(130, 81, 230, 0.7) 40%,
                rgba(130, 81, 230, 0.4) 70%,
                rgba(130, 81, 230, 0.2) 85%,
                rgba(130, 81, 230, 0.1) 95%,
                transparent 100%
              );
            }

            #video-header::before,
            #video-header::after {
              display: none;
            }

            #video-header h2 {
              font-size: 1rem;
              background: none;
              -webkit-background-clip: unset;
              background-clip: unset;
              -webkit-text-fill-color: white;
              filter: none;
            }

            #video-header p {
              font-size: 0.875rem;
            }

            #remoteVideo {
              border-radius: 0;
              box-shadow: none;
            }

            #remoteVideo::before {
              display: none;
            }

            .blur-border-edges,
            .blur-border-corner {
              display: none;
            }

            .loading-indicator {
              backdrop-filter: blur(8px);
              -webkit-backdrop-filter: blur(8px);
            }
          }
        </style>
      </head>
      <body class="light bg-gray-600 dark:bg-primary-50 h-screen flex items-center justify-center">
        <div
          class="relative md:h-[750px] md:w-[435px] md:rounded-xl shadow-md shadow-gray-400 bg-white"
        >
          <main
            id="connection-flow"
            class="h-screen w-screen flex flex-col items-center justify-center bg-white text-black md:h-[750px] md:w-[435px] md:rounded-xl"
            ws-connect="${wsEndpoint}/agents/connections/${userId}:${props.serviceId}"
            hx-ext="ws"
          ></main>

          <div
            id="video-container"
            class="absolute inset-0 rounded-lg overflow-hidden w-full h-full transition-all duration-300 ease-in-out hidden z-10"
            style="display: none;"
          >
            <div id="video-header" class="flex-shrink-0 z-30 p-4 pointer-events-none">
              <div class="text-center">
                <h2 class="text-white text-lg font-semibold mb-1 drop-shadow-lg">
                  Interactive Session
                </h2>
                <p class="text-white/90 text-sm drop-shadow-md">
                  Click and interact with the live captcha
                </p>
              </div>
            </div>

            <div id="blur-border-overlay" class="relative flex-1 w-full">
              <div class="blur-border-edges blur-border-top"></div>
              <div class="blur-border-edges blur-border-bottom"></div>
              <div class="blur-border-edges blur-border-left"></div>
              <div class="blur-border-edges blur-border-right"></div>
              <div class="blur-border-corner blur-border-corner-tl"></div>
              <div class="blur-border-corner blur-border-corner-tr"></div>
              <div class="blur-border-corner blur-border-corner-bl"></div>
              <div class="blur-border-corner blur-border-corner-br"></div>
            </div>

            <div
              id="interactivity-overlay"
              class="absolute inset-0 bg-transparent z-10 hidden cursor-not-allowed transition-opacity opacity-0"
            >
              <div class="flex items-center justify-center h-full w-full">
                <div class="loading-indicator px-4 py-2 rounded-xl flex items-center">
                  <div class="animate-spin mr-3 h-5 w-5 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle
                        class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"
                      ></circle>
                      <path
                        class="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                  </div>
                  <span class="text-sm font-medium text-gray-800">Processing...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <script>
          const { pathname } = window.location;
          const agentPath = pathname.replace(/^\\/+|\\/+$/g, '');
          const socket = new WebSocket('${wsEndpoint}');

          let pc;
          let remoteStream;
          let video;
          let webRTCInitialized = false;
          let cropBoxBoundingRect = { width: 1366, height: 768, x: 0, y: 0 };
          let currentInputBoxRects = []; // Store current input overlay rectangles
          let currentFocusedOverlay = null; // Track currently focused input overlay

          let debugMode = true;
          let isVideoPaused = false;
          let frameRequestRetries = 0;
          const MAX_FRAME_REQUEST_RETRIES = 4;
          const FRAME_REQUEST_TIMEOUT = 3000; // 3 seconds
          let frameRequestTimer = null;
          let lastFrameReceived = 0;
          let frameReceiveCheckInterval = null;

          /**
           * Request a new frame with retry logic for remote environments
           * Uses exponential backoff and multiple attempts to ensure frame generation
           */
          const requestFrameWithRetry = () => {
            // Clear any existing timer
            if (frameRequestTimer) {
              clearTimeout(frameRequestTimer);
              frameRequestTimer = null;
            }
            const attemptFrameRequest = (attempt = 0) => {
              if (attempt >= MAX_FRAME_REQUEST_RETRIES) {
                console.warn('Max frame request retries reached, giving up');
                frameRequestRetries = 0;
                return;
              }
              console.log(
                'Requesting frame (attempt ' +
                  (attempt + 1) +
                  '/' +
                  MAX_FRAME_REQUEST_RETRIES +
                  ')',
              );
              socket.send(JSON.stringify({ type: 'request-frame' }));
              // Set timeout for next retry with exponential backoff
              const retryDelay = Math.min(FRAME_REQUEST_TIMEOUT * Math.pow(1.5, attempt), 5000);
              frameRequestTimer = setTimeout(() => {
                attemptFrameRequest(attempt + 1);
              }, retryDelay);
            };
            frameRequestRetries++;
            attemptFrameRequest();
          };
          /**
           * Stop frame requesting and clear any pending retries
           */
          const stopFrameRequesting = () => {
            if (frameRequestTimer) {
              clearTimeout(frameRequestTimer);
              frameRequestTimer = null;
            }
            frameRequestRetries = 0;
          };

          const updateVideoContainer = () => {
            if (!video) return;

            const videoContainer = document.getElementById('video-container');
            const blurBorderOverlay = document.getElementById('blur-border-overlay');
            const videoHeader = document.getElementById('video-header');
            const connectionFlow = document.getElementById('connection-flow');

            if (videoContainer && blurBorderOverlay && videoHeader) {
              const aspectRatio = cropBoxBoundingRect.width / cropBoxBoundingRect.height;
              const isMobile = window.innerWidth < 768;

              // Use the parent container dimensions
              const parentWidth = videoContainer.parentElement?.clientWidth || window.innerWidth;
              const parentHeight = videoContainer.parentElement?.clientHeight || window.innerHeight;

              const headerHeight = videoHeader.offsetHeight;

              let containerWidth, containerHeight;
              let minMargin = 20;
              containerWidth = parentWidth - minMargin;

              const availableHeight = parentHeight - headerHeight - minMargin;
              containerHeight = containerWidth / aspectRatio;

              if (containerHeight > availableHeight) {
                containerHeight = availableHeight;
                containerWidth = containerHeight * aspectRatio;
              }

              const totalContainerHeight = containerHeight + headerHeight;

              // Apply dimensions with a small delay to ensure smooth transition
              requestAnimationFrame(() => {
                videoContainer.style.width = containerWidth + 'px';
                videoContainer.style.height = totalContainerHeight + 'px';
                videoContainer.style.position = 'absolute';
                videoContainer.style.top = '50%';
                videoContainer.style.left = '50%';
                videoContainer.style.transform = 'translate(-50%, -50%)';

                // The blur border overlay should only take the video area (not including header)
                blurBorderOverlay.style.width = containerWidth + 'px';
                blurBorderOverlay.style.height = containerHeight + 'px';
                blurBorderOverlay.style.display = 'block';

                if (video) {
                  video.style.objectFit = 'contain';
                }
              });

              console.log('Updated container and blur border dimensions:', {
                containerWidth: containerWidth,
                containerHeight: containerHeight,
                totalContainerHeight: totalContainerHeight,
                headerHeight: headerHeight,
                aspectRatio: aspectRatio,
                isMobile: isMobile,
                cropBox: cropBoxBoundingRect,
              });

              // Wait for animations to complete, then handle input overlays
              setTimeout(() => {
                const existingOverlays = document.querySelectorAll('.input-overlay');
                if (
                  currentInputBoxRects &&
                  currentInputBoxRects.length > 0 &&
                  existingOverlays.length === 0
                ) {
                  console.log(
                    'Recreating input overlays after video container update (no existing overlays found)',
                  );
                  createInputOverlays(currentInputBoxRects);
                } else if (existingOverlays.length > 0) {
                  console.log(
                    'Input overlays already exist, repositioning after video container update',
                  );
                  // Reposition existing overlays after container update
                  debouncedRepositionInputOverlays();
                }
              }, 300); // Wait for CSS transitions to complete
            }
          };

          /**
           * Handles focusing an input overlay and activating keyboard
           * @param {HTMLInputElement} overlay - The overlay element to focus
           */
          const focusInputOverlay = (overlay) => {
            if (currentFocusedOverlay && currentFocusedOverlay !== overlay) {
              blurInputOverlay(currentFocusedOverlay);
            }

            currentFocusedOverlay = overlay;
            overlay.focus();
          };

          /**
           * Handles blurring an input overlay and dismissing keyboard
           * @param {HTMLInputElement} overlay - The overlay element to blur
           */
          const blurInputOverlay = (overlay) => {
            if (!overlay) return;

            overlay.blur();

            if (currentFocusedOverlay === overlay) {
              currentFocusedOverlay = null;
            }
          };

          /**
           * Sets up global click listener to handle blur when clicking outside video and input overlays
           */
          const setupGlobalInputBlurListener = () => {
            if (window.globalInputBlurListener) {
              document.removeEventListener('click', window.globalInputBlurListener, true);
            }

            window.globalInputBlurListener = (e) => {
              const clickedOverlay = e.target.closest('.input-overlay');
              const clickedOnVideo = e.target === video || video?.contains(e.target);

              if (!clickedOverlay && !clickedOnVideo && currentFocusedOverlay) {
                blurInputOverlay(currentFocusedOverlay);
              }
            };

            document.addEventListener('click', window.globalInputBlurListener, true);
          };

          /**
           * Creates interactive input overlay elements for input fields.
           * Uses simplified input/beforeinput event approach for character-based transmission.
           * @param {Array} inputBoxRects - Array of input element rectangles
           */
          const createInputOverlays = (inputBoxRects) => {
            const existingOverlays = document.querySelectorAll('.input-overlay');
            if (existingOverlays.length > 0) {
              existingOverlays.forEach((overlay) => overlay.remove());
            }

            currentFocusedOverlay = null;

            if (!video || !inputBoxRects || inputBoxRects.length === 0) {
              return;
            }

            setupGlobalInputBlurListener();

            const videoContainer = document.getElementById('video-container');
            const blurBorderOverlay = document.getElementById('blur-border-overlay');
            const videoHeader = document.getElementById('video-header');

            if (!videoContainer || !blurBorderOverlay) {
              console.warn('Video container or blur border overlay not found');
              return;
            }

            const containerBounds = videoContainer.getBoundingClientRect();
            const videoBounds = video.getBoundingClientRect();
            const headerHeight = videoHeader ? videoHeader.offsetHeight : 0;

            inputBoxRects.forEach((inputRect, index) => {
              const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);

              if (overlayCoords) {
                // Create actual input element for iOS Safari keyboard activation requirements
                const overlay = document.createElement('input');
                overlay.type = 'text';
                overlay.className = 'input-overlay';
                overlay.id = 'input-overlay-' + index;
                overlay.style.position = 'absolute';

                overlay.style.left = overlayCoords.x + 'px';
                overlay.style.top = overlayCoords.y + 'px';
                overlay.style.width = overlayCoords.width + 'px';
                overlay.style.height = overlayCoords.height + 'px';

                // Transparent styling for production invisibility
                overlay.style.backgroundColor = 'transparent';
                overlay.style.border = '1px solid rgba(0, 0, 0, 0.01)';
                overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.01)';
                overlay.style.color = 'rgba(0, 0, 0, 0.01)';
                overlay.style.caretColor = 'rgba(0, 0, 0, 0.01)';

                overlay.style.zIndex = '1000';
                overlay.style.pointerEvents = 'auto';
                overlay.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                overlay.style.outline = 'none';
                overlay.style.fontSize = '16px'; // 16px prevents zoom on iOS Safari
                overlay.style.padding = '2px 8px';
                overlay.style.color = 'transparent';

                overlay.addEventListener('focus', () => {
                  focusInputOverlay(overlay);
                });

                overlay.addEventListener('blur', () => {
                  blurInputOverlay(overlay);
                });

                // Simplified input handling - process all input events immediately

                overlay.addEventListener('input', (e) => {
                  const inputType = e.inputType;
                  const data = e.data;
                  const currentValue = e.target.value;

                  // Clear the input immediately to prevent accumulation
                  e.target.value = '';

                  if (window.sendInput) {
                    try {
                      // Handle all character insertion types immediately
                      if (inputType === 'insertText' && data) {
                        window.sendInput('char-input', {
                          text: data,
                          inputType: inputType,
                          timestamp: Date.now(),
                          source: 'input-event',
                        });
                      } else if (inputType === 'insertCompositionText' && data) {
                        const lastChar = currentValue.charAt(currentValue.length - 1);
                        window.sendInput('char-input', {
                          text: lastChar,
                          inputType: inputType,
                          timestamp: Date.now(),
                          source: 'input-event-ime',
                        });
                      } else if (inputType === 'insertFromPaste' && data) {
                        window.sendInput('text-insert', {
                          text: data,
                          inputType: inputType,
                          timestamp: Date.now(),
                          source: 'input-event-paste',
                        });
                      } else if (inputType === 'deleteContentBackward') {
                        window.sendInput('char-input', {
                          text: '', // Backspace character
                          inputType: inputType,
                          timestamp: Date.now(),
                          source: 'input-event-backspace',
                        });
                      } else if (inputType === 'deleteContentForward') {
                        window.sendInput('char-input', {
                          text: '', // Delete character
                          inputType: inputType,
                          timestamp: Date.now(),
                          source: 'input-event-delete',
                        });
                      } else if (currentValue && currentValue.length > 0) {
                        // Only send the last character to prevent accumulation
                        const lastChar = currentValue.charAt(currentValue.length - 1);
                        window.sendInput('char-input', {
                          text: lastChar,
                          inputType: inputType || 'unknown',
                          timestamp: Date.now(),
                          source: 'input-event-fallback',
                        });
                      }
                    } catch (error) {
                      console.error('Error sending input:', error);
                    }
                  } else {
                    console.warn('sendInput not available');
                  }
                });

                // Handle essential navigation keys only - character input handled by input events
                overlay.addEventListener('keydown', (e) => {
                  const NAVIGATION_KEYS = [
                    'Tab',
                    'Escape',
                    'ArrowUp',
                    'ArrowDown',
                    'ArrowLeft',
                    'ArrowRight',
                    'Backspace',
                    'Home',
                    'End',
                    'PageUp',
                    'PageDown',
                    'F1',
                    'F2',
                    'F3',
                    'F4',
                    'F5',
                    'F6',
                    'F7',
                    'F8',
                    'F9',
                    'F10',
                    'F11',
                    'F12',
                  ];

                  const isNavigationKey = NAVIGATION_KEYS.includes(e.key);
                  const isModifierCombo = e.ctrlKey || e.metaKey || e.altKey;

                  if (window.sendInput && (isNavigationKey || isModifierCombo)) {
                    window.sendInput('navigation-key', {
                      key: e.key,
                      code: e.code,
                      shiftKey: e.shiftKey,
                      ctrlKey: e.ctrlKey,
                      altKey: e.altKey,
                      metaKey: e.metaKey,
                      timestamp: Date.now(),
                      source: 'navigation',
                    });
                  }
                });

                // Handle click events with coordinate transformation to remote browser
                overlay.addEventListener('click', (e) => {
                  const overlayBounds = overlay.getBoundingClientRect();
                  const relativeX = (e.clientX - overlayBounds.left) / overlayBounds.width;
                  const relativeY = (e.clientY - overlayBounds.top) / overlayBounds.height;

                  // Transform to remote browser coordinates
                  const remoteX = inputRect.x + relativeX * inputRect.width;
                  const remoteY = inputRect.y + relativeY * inputRect.height;

                  if (window.sendInput) {
                    window.sendInput('click', {
                      x: Math.floor(remoteX),
                      y: Math.floor(remoteY),
                      button: 0,
                    });
                  }
                });

                // Handle touch events for mobile - iOS Safari keyboard activation requirements
                overlay.addEventListener('touchend', (e) => {
                  if (e.changedTouches.length === 1) {
                    const touch = e.changedTouches[0];

                    // Calculate touch position and transform to remote browser coordinates
                    const overlayBounds = overlay.getBoundingClientRect();
                    const relativeX = (touch.clientX - overlayBounds.left) / overlayBounds.width;
                    const relativeY = (touch.clientY - overlayBounds.top) / overlayBounds.height;

                    const remoteX = inputRect.x + relativeX * inputRect.width;
                    const remoteY = inputRect.y + relativeY * inputRect.height;

                    if (window.sendInput) {
                      window.sendInput('click', {
                        x: Math.floor(remoteX),
                        y: Math.floor(remoteY),
                        button: 0,
                      });
                    }
                  }
                });

                blurBorderOverlay.appendChild(overlay);
              }
            });
          };

          /**
           * Transforms input element coordinates to video overlay coordinates
           * @param {Object} inputRect - Input element rectangle {x, y, width, height}
           * @param {DOMRect} videoBounds - Video element bounding rectangle
           * @returns {Object|null} Overlay coordinates or null if outside crop area
           */
          const transformInputCoordsToVideoOverlay = (inputRect, videoBounds) => {
            // Check if input is within the crop box
            if (
              inputRect.x < cropBoxBoundingRect.x ||
              inputRect.y < cropBoxBoundingRect.y ||
              inputRect.x + inputRect.width > cropBoxBoundingRect.x + cropBoxBoundingRect.width ||
              inputRect.y + inputRect.height > cropBoxBoundingRect.y + cropBoxBoundingRect.height
            ) {
              return null; // Input is outside the visible crop area
            }

            // Calculate relative position within crop box (0-1 range)
            const relativeX = (inputRect.x - cropBoxBoundingRect.x) / cropBoxBoundingRect.width;
            const relativeY = (inputRect.y - cropBoxBoundingRect.y) / cropBoxBoundingRect.height;
            const relativeWidth = inputRect.width / cropBoxBoundingRect.width;
            const relativeHeight = inputRect.height / cropBoxBoundingRect.height;

            // Get video container positioning context
            const videoContainer = document.getElementById('video-container');
            const blurBorderOverlay = document.getElementById('blur-border-overlay');

            if (!videoContainer || !blurBorderOverlay) {
              console.warn('Video container elements not found for coordinate transformation');
              return null;
            }

            // Get the actual video display area (accounting for object-fit: contain)
            const containerBounds = blurBorderOverlay.getBoundingClientRect();
            const videoAspectRatio = cropBoxBoundingRect.width / cropBoxBoundingRect.height;
            const containerAspectRatio = containerBounds.width / containerBounds.height;

            let videoDisplayWidth, videoDisplayHeight, videoOffsetX, videoOffsetY;

            if (videoAspectRatio > containerAspectRatio) {
              // Video is wider than container - letterboxed top/bottom
              videoDisplayWidth = containerBounds.width;
              videoDisplayHeight = containerBounds.width / videoAspectRatio;
              videoOffsetX = 0;
              videoOffsetY = (containerBounds.height - videoDisplayHeight) / 2;
            } else {
              // Video is taller than container - letterboxed left/right
              videoDisplayWidth = containerBounds.height * videoAspectRatio;
              videoDisplayHeight = containerBounds.height;
              videoOffsetX = (containerBounds.width - videoDisplayWidth) / 2;
              videoOffsetY = 0;
            }

            // Transform to video overlay coordinates within the display area
            return {
              x: videoOffsetX + relativeX * videoDisplayWidth,
              y: videoOffsetY + relativeY * videoDisplayHeight,
              width: relativeWidth * videoDisplayWidth,
              height: relativeHeight * videoDisplayHeight,
            };
          };

          const initWebRTC = () => {
            pc = new RTCPeerConnection({
              iceServers: [
                {
                  urls: 'stun:stun.cloudflare.com:3478',
                },
                {
                  urls: 'turn:relay1.expressturn.com:3478',
                  username: 'ef89RMU4SHUQMSOUU9',
                  credential: 'jvkMMnQxWX4Qrhe3',
                },
              ],
            });

            pc.ondatachannel = (event) => {
              const inputChannel = event.channel;

              window.sendInput = (type, payload) => {
                inputChannel.send(JSON.stringify({ type, ...payload }));
              };

              inputChannel.onopen = () => {
                console.log('Input channel ready to send');
                requestFrameWithRetry();
              };

              // Handle incoming messages from the remote peer (screen-cropper)
              inputChannel.onmessage = (event) => {
                try {
                  const data = JSON.parse(event.data);
                } catch (err) {
                  console.error('Failed to parse data channel message:', err);
                }
              };
            };

            remoteStream = new MediaStream();
            if (!video) {
              video = document.createElement('video');
            }
            video.id = 'remoteVideo';
            video.autoplay = true;
            video.playsInline = true;
            video.muted = true;
            video.setAttribute('playsinline', '');
            video.srcObject = remoteStream;

            // Apply styles
            Object.assign(video.style, {
              width: '100%',
              height: '100%',
              objectFit: 'contain',
              display: 'block',
              position: 'relative',
              touchAction: 'manipulation',
              transition: 'all 300ms ease-in-out',
            });

            const blurBorderOverlay = document.getElementById('blur-border-overlay');
            blurBorderOverlay.appendChild(video);

            updateVideoContainer();

            pc.onicecandidate = (event) => {
              if (event.candidate) {
                socket.send(JSON.stringify({ type: 'candidate', candidate: event.candidate }));
              }
            };

            pc.ontrack = (e) => {
              console.log('Received remote track');
              e.streams[0].getTracks().forEach((track) => remoteStream.addTrack(track));
            };

            setupInputListeners();
          };

          const pauseVideo = () => {
            if (isVideoPaused || !video) return;

            console.log('Pausing video stream');

            try {
              video.pause();
            } catch (err) {
              console.warn('Error pausing video:', err);
            }

            const overlay = document.getElementById('interactivity-overlay');
            if (overlay) {
              overlay.classList.remove('hidden');
              overlay.style.opacity = '1';
            }
            video.style.pointerEvents = 'none';
            isVideoPaused = true;
          };

          const resumeVideo = () => {
            if (!isVideoPaused || !video) return;

            console.log('Resuming video stream');

            video.play().catch((err) => {
              console.warn('Error playing video:', err);
            });

            const overlay = document.getElementById('interactivity-overlay');
            if (overlay) {
              overlay.style.opacity = '0';
              setTimeout(() => {
                overlay.classList.add('hidden');
              }, 300);
            }
            updateVideoContainer();
            video.style.pointerEvents = 'auto';
            isVideoPaused = false;
          };

          const showVideoContainer = () => {
            const videoContainer = document.getElementById('video-container');
            const connectionFlow = document.getElementById('connection-flow');

            if (videoContainer && connectionFlow) {
              connectionFlow.style.display = 'none';
              videoContainer.style.display = 'flex';
              videoContainer.style.flexDirection = 'column';
              videoContainer.classList.remove('hidden');
              updateVideoContainer();
            }
          };

          const hideVideoContainer = () => {
            const videoContainer = document.getElementById('video-container');
            const connectionFlow = document.getElementById('connection-flow');

            if (videoContainer && connectionFlow) {
              videoContainer.style.display = 'none';
              videoContainer.classList.add('hidden');
              connectionFlow.style.display = 'flex';
            }
          };

          /**
           * Checks if a click event is within any input overlay area
           * @param {MouseEvent|Touch} e - The click/touch event
           * @returns {boolean} True if click is within an input overlay area
           */
          const isClickWithinInputOverlayArea = (e) => {
            if (!currentInputBoxRects || currentInputBoxRects.length === 0) {
              return false;
            }

            const videoBounds = video.getBoundingClientRect();
            if (!videoBounds) {
              return false;
            }

            // Get click coordinates relative to video
            const clickX = e.clientX - videoBounds.left;
            const clickY = e.clientY - videoBounds.top;

            // Check if click is within any input overlay area
            for (const inputRect of currentInputBoxRects) {
              const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);
              if (overlayCoords) {
                // Check if click is within this overlay area
                if (
                  clickX >= overlayCoords.x &&
                  clickX <= overlayCoords.x + overlayCoords.width &&
                  clickY >= overlayCoords.y &&
                  clickY <= overlayCoords.y + overlayCoords.height
                ) {
                  return true;
                }
              }
            }
            return false;
          };

          const setupInputListeners = () => {
            let isDragging = false; // Track drag state
            let lastMousePosition = { x: 0, y: 0 }; // Store last position for drag termination
            let lastMoveTime = 0; // For debouncing mousemove
            const MOVE_DEBOUNCE_MS = 10; // Send mousemove every 10ms

            const sendMouseEvent = (type, e) => {
              let clientX, clientY;

              if (e instanceof MouseEvent || e instanceof Touch) {
                clientX = e.clientX;
                clientY = e.clientY;
              } else {
                console.warn('Unknown event type in sendMouseEvent');
                return;
              }

              const bounds = video.getBoundingClientRect();

              // Ensure coordinates are within bounds
              if (
                clientX < bounds.left ||
                clientX > bounds.right ||
                clientY < bounds.top ||
                clientY > bounds.bottom
              ) {
                console.log('Ignoring event outside video bounds');
                return;
              }

              const relativeX = (clientX - bounds.left) / bounds.width;
              const relativeY = (clientY - bounds.top) / bounds.height;

              const scaledX = Math.floor(relativeX * cropBoxBoundingRect.width);
              const scaledY = Math.floor(relativeY * cropBoxBoundingRect.height);
              const translatedX = scaledX + cropBoxBoundingRect.x;
              const translatedY = scaledY + cropBoxBoundingRect.y;

              // Update last position for drag termination
              lastMousePosition = { x: translatedX, y: translatedY };

              if (
                window.sendInput &&
                (type !== 'mousemove' || Date.now() - lastMoveTime >= MOVE_DEBOUNCE_MS)
              ) {
                window.sendInput(type, { x: translatedX, y: translatedY, button: e.button || 0 });
                if (type === 'mousemove') {
                  lastMoveTime = Date.now();
                }
              } else if (!window.sendInput) {
                console.warn('Input channel not ready');
              }
            };

            const interactivityOverlay = document.getElementById('interactivity-overlay');
            if (interactivityOverlay) {
              interactivityOverlay.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('Click blocked during processing');
                return false;
              };
              // Prevent drag events from being blocked by overlay
              interactivityOverlay.onmousedown = (e) => {
                e.preventDefault();
                e.stopPropagation();
                return false;
              };
              interactivityOverlay.ontouchstart = (e) => {
                e.preventDefault();
                e.stopPropagation();
                return false;
              };
            }

            /**
             * Enhanced video click handler that blurs input overlays when clicking on empty video areas
             */
            video.onclick = (e) => {
              // Check if click is within any input overlay area
              const clickedOnInputOverlay = isClickWithinInputOverlayArea(e);

              if (!clickedOnInputOverlay && currentFocusedOverlay) {
                console.log(
                  '🔴 [INPUT OVERLAY] Click on empty video area, blurring focused overlay',
                );
                blurInputOverlay(currentFocusedOverlay);
              }
            };

            // Mouse events for desktop
            video.onmousedown = (e) => {
              isDragging = true;
              sendMouseEvent('mousedown', e);
            };

            video.onmousemove = (e) => {
              if (isDragging) {
                sendMouseEvent('mousemove', e);
              }
            };

            video.onmouseup = (e) => {
              if (isDragging) {
                sendMouseEvent('mouseup', e);
                isDragging = false;
              }
              // Send click only for non-drag interactions
              if (!isDragging) {
              }
            };

            video.onmouseleave = () => {
              if (isDragging) {
                sendMouseEvent('mouseup', lastMousePosition);
                isDragging = false;
              }
            };

            // Touch events for mobile
            let touchStartTime = 0;
            let lastTouchX = 0;
            let lastTouchY = 0;
            const CLICK_DELAY_THRESHOLD = 300;
            const MOVE_THRESHOLD = 10;

            video.addEventListener(
              'touchstart',
              (e) => {
                e.preventDefault();
                if (e.touches.length === 1) {
                  e.preventDefault(); // Prevent scrolling
                  const touch = e.touches[0];
                  lastTouchX = touch.clientX;
                  lastTouchY = touch.clientY;
                  touchStartTime = Date.now();
                  isDragging = true;
                  sendMouseEvent('mousedown', touch);
                }
              },
              { passive: false },
            );

            video.addEventListener(
              'touchmove',
              (e) => {
                e.preventDefault();
                if (e.touches.length === 1 && isDragging) {
                  const touch = e.touches[0];
                  const deltaX = Math.abs(touch.clientX - lastTouchX);
                  const deltaY = Math.abs(touch.clientY - lastTouchY);

                  if (deltaX > MOVE_THRESHOLD || deltaY > MOVE_THRESHOLD) {
                    lastTouchX = touch.clientX;
                    lastTouchY = touch.clientY;
                    sendMouseEvent('mousemove', touch);
                  }
                }
              },
              { passive: false },
            );

            video.addEventListener(
              'touchend',
              (e) => {
                e.preventDefault();
                const touchDuration = Date.now() - touchStartTime;

                if (e.changedTouches.length === 1 && isDragging) {
                  const touch = e.changedTouches[0];
                  sendMouseEvent('mouseup', touch);
                  const touchDuration = Date.now() - touchStartTime;
                  const deltaX = Math.abs(touch.clientX - lastTouchX);
                  const deltaY = Math.abs(touch.clientY - lastTouchY);

                  // Send click only for short, non-dragging touches
                  if (
                    touchDuration < CLICK_DELAY_THRESHOLD &&
                    deltaX < MOVE_THRESHOLD &&
                    deltaY < MOVE_THRESHOLD
                  ) {
                    // Check if touch is within any input overlay area
                    const touchedOnInputOverlay = isClickWithinInputOverlayArea(touch);

                    if (!touchedOnInputOverlay && currentFocusedOverlay) {
                      console.log(
                        '🔴 [INPUT OVERLAY] Touch on empty video area, blurring focused overlay',
                      );
                      blurInputOverlay(currentFocusedOverlay);
                    }
                  }
                  isDragging = false;
                }
              },
              { passive: true },
            );

            video.addEventListener(
              'touchcancel',
              () => {
                if (isDragging) {
                  sendMouseEvent('mouseup', lastMousePosition);
                  isDragging = false;
                }
              },
              { passive: false },
            );

            video.addEventListener(
              'touchcancel',
              (e) => {
                e.preventDefault();
                touchStartTime = 0;
                lastTouchX = 0;
                lastTouchY = 0;
              },
              { passive: false },
            );

            // Video keyboard events removed - all input handled by input overlays with modern input events
          };

          const handleSocketMessage = async (event) => {
            try {
              const msg = JSON.parse(event.data);
              if (msg.type === 'offer') {
                console.log('Received offer', msg);
                await pc.setRemoteDescription(new RTCSessionDescription(msg.offer));
                const answer = await pc.createAnswer();
                await pc.setLocalDescription(answer);
                socket.send(JSON.stringify({ type: 'answer', answer }));
              } else if (msg.type === 'candidate') {
                console.log('Received ICE candidate', msg);
                await pc.addIceCandidate(new RTCIceCandidate(msg.candidate));
              } else if (msg.type === 'answer') {
                console.log('Received answer', msg);
                await pc.setRemoteDescription(new RTCSessionDescription(msg.answer));
              } else if (msg.type === 'interactivity-status') {
                if (msg.status === 'paused') {
                  console.log('Pausing interactivity', cropBoxBoundingRect);
                  pauseVideo();
                } else if (msg.status === 'enabled') {
                  console.log('Resuming interactivity', msg.cropBox);
                  cropBoxBoundingRect = msg.cropBox;

                  // Update input overlays if provided
                  if (msg.inputBoxRects) {
                    currentInputBoxRects = msg.inputBoxRects;
                    console.log('Received input box rects:', currentInputBoxRects);
                  }

                  if (video) {
                    updateVideoContainer();

                    // Create input overlays after video container is updated (to avoid race condition)
                    if (msg.inputBoxRects && currentInputBoxRects.length > 0) {
                      setTimeout(() => {
                        console.log('Creating input overlays after video container update');
                        createInputOverlays(currentInputBoxRects);
                      }, 300); // Wait for CSS transitions to complete
                    }
                  }

                  if (!webRTCInitialized) {
                    webRTCInitialized = true;
                    initWebRTC();
                    socket.send(JSON.stringify({ type: 'ready' }));

                    setTimeout(() => {
                      try {
                        console.log('Showing video container');
                        showVideoContainer();
                      } catch (err) {
                        console.error('Error showing video container:', err);
                      }
                    }, 500);
                  } else {
                    resumeVideo();
                    requestFrameWithRetry();
                    showVideoContainer();
                  }
                } else if (msg.type === 'no-difference-detected') {
                  console.log('No significant difference detected, resuming video');
                  resumeVideo();
                  socket.send(JSON.stringify({ type: 'request-frame' }));
                  showVideoContainer();
                }
              } else if (msg.type === 'no-difference-detected') {
                console.log('No significant difference detected, resuming video');
                resumeVideo();
                showVideoContainer();
              }
            } catch (e) {}
          };

          socket.addEventListener('open', () => {
            console.log('WebSocket connected and ready to receive trigger.');
          });

          socket.addEventListener('message', handleSocketMessage);

          document.addEventListener('retry-request', () => {
            if (socket && socket.readyState === WebSocket.OPEN) {
              socket.send(JSON.stringify({ type: 'retry' }));
            }
          });

          /**
           * Repositions existing input overlays when video container layout changes
           * This is crucial for mobile keyboard appearance/dismissal
           */
          let repositionTimeout = null;
          const repositionInputOverlays = () => {
            const existingOverlays = document.querySelectorAll('.input-overlay');
            if (
              existingOverlays.length === 0 ||
              !currentInputBoxRects ||
              currentInputBoxRects.length === 0
            ) {
              return;
            }

            console.log(
              '📱 [INPUT OVERLAY] Repositioning',
              existingOverlays.length,
              'overlays for layout change',
            );

            const videoBounds = video ? video.getBoundingClientRect() : null;
            if (!videoBounds) {
              console.warn('❌ [INPUT OVERLAY] Cannot reposition - video bounds not available');
              return;
            }

            existingOverlays.forEach((overlay, index) => {
              const inputRect = currentInputBoxRects[index];
              if (inputRect) {
                const overlayCoords = transformInputCoordsToVideoOverlay(inputRect, videoBounds);
                if (overlayCoords) {
                  // Update overlay position
                  overlay.style.left = overlayCoords.x + 'px';
                  overlay.style.top = overlayCoords.y + 'px';
                  overlay.style.width = overlayCoords.width + 'px';
                  overlay.style.height = overlayCoords.height + 'px';

                  console.log('📍 [INPUT OVERLAY] Repositioned overlay', overlay.id, 'to:', {
                    x: overlayCoords.x,
                    y: overlayCoords.y,
                    width: overlayCoords.width,
                    height: overlayCoords.height,
                  });
                }
              }
            });
          };

          /**
           * Debounced repositioning to handle rapid layout changes
           */
          const debouncedRepositionInputOverlays = () => {
            if (repositionTimeout) {
              clearTimeout(repositionTimeout);
            }
            repositionTimeout = setTimeout(() => {
              repositionInputOverlays();
            }, 300); // Wait for CSS transitions to complete
          };

          /**
           * Manages input overlay active state to prevent transform conflicts
           */
          const setInputOverlayActiveState = (active) => {
            const videoContainer = document.getElementById('video-container');
            if (videoContainer) {
              if (active) {
                videoContainer.classList.add('input-overlay-active');
              } else {
                videoContainer.classList.remove('input-overlay-active');
              }
            }
          };

          // Initial check for mobile orientation
          if (window.innerWidth < 768) {
            console.log('Mobile device detected, optimizing layout');
          }

          // Add viewport change listeners for mobile keyboard and orientation changes
          let resizeTimeout = null;
          const handleViewportChange = () => {
            if (resizeTimeout) {
              clearTimeout(resizeTimeout);
            }
            resizeTimeout = setTimeout(() => {
              console.log('📱 [VIEWPORT] Viewport changed, repositioning overlays');
              debouncedRepositionInputOverlays();
            }, 300); // Wait for viewport changes to settle
          };

          // Listen for window resize (includes mobile keyboard appearance/dismissal)
          window.addEventListener('resize', handleViewportChange);

          // Listen for orientation changes on mobile
          window.addEventListener('orientationchange', () => {
            setTimeout(handleViewportChange, 300); // iOS needs extra delay after orientation change
          });

          // Listen for visual viewport changes (mobile keyboard)
          if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', handleViewportChange);
          }
        </script>
      </body>
    </html>`;
};
