export type PlatformTypes = 'facebook' | 'github' | 'google' | 'test' | 'login_test' | 'text_captcha';

interface PlatformDetails {
  name: string;
  logo: string;
}

export const platformDetails: Record<string, PlatformDetails> = {
  facebook: {
    name: 'Facebook',
    logo: '/fb.png',
  },
  github: {
    name: 'Gith<PERSON>',
    logo: '/github.png',
  },
  google: {
    name: 'Google',
    logo: '/google.png',
  },
  test: {
    name: 'Test',
    logo: '/google.png',
  },
  login_test: {
    name: 'Test',
    logo: '/google.png',
  },
  text_captcha: {
    name: 'Text Test',
    logo: '/fb.png'
  }
};
