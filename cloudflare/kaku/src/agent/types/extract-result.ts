import { BoundingRect } from './agent-state';

export type ExtractResult = {
  extractedData: PageStateResult;
};

export type Action = {
  type: 'click' | 'fill';
  name: string;
  value?: string;
  coordinates: {
    x: number;
    y: number;
  };
  order: number; // Position in the visual flow, top to bottom
  isSubmitAction?: boolean; // Flag to identify submit/confirmation actions that should be last
};

export type PageStateResult = {
  formTitle: string;
  formDescription: string;
  errors: string[];
  pageType:
    | 'credentials'
    | 'otp'
    | 'captcha'
    | '2-factor-auth'
    | 'authenticated'
    | 'other'
    | 'loading';
  htmxForm: string;
  actions: Action[];
};

export type CaptchaBoundingBox = Omit<BoundingRect, 'id'>;

export type CaptchaBoundingBox4Edges = {
  x: number;
  y: number;
  x2: number;
  y2: number;
};

export function convertBoundingBox4EdgesToBoundingBox(
  boundingBox: CaptchaBoundingBox4Edges,
): CaptchaBoundingBox {
  const x = boundingBox.x;
  const y = boundingBox.y;

  const originalWidth = boundingBox.x2 - boundingBox.x;
  const originalHeight = boundingBox.y2 - boundingBox.y;

  const width = originalWidth;
  const height = originalHeight;

  return { x, y, width, height };
}

/**
 * Expands a bounding box by a fixed number of pixels on all sides.
 * Ensures the expanded box doesn't exceed screen boundaries.
 *
 * @param boundingBox The original bounding box to expand
 * @param expansionPixels Number of pixels to expand on each side
 * @param viewportWidth The width of the viewport
 * @param viewportHeight The height of the viewport
 * @returns The expanded bounding box and a record of which sides were fully expanded
 */
export function expandBoundingBox(
  boundingBox: CaptchaBoundingBox,
  expansionPixels: number,
  viewportWidth: number,
  viewportHeight: number,
): {
  expandedBox: CaptchaBoundingBox;
  expansionLimits: {
    left: boolean;
    right: boolean;
    top: boolean;
    bottom: boolean;
  };
} {
  // Calculate expanded dimensions
  const expandedX = Math.max(0, boundingBox.x - expansionPixels);
  const expandedY = Math.max(0, boundingBox.y - expansionPixels);

  // Calculate how much we could expand in each direction
  const actualLeftExpansion = boundingBox.x - expandedX;
  const actualTopExpansion = boundingBox.y - expandedY;

  // Calculate right and bottom edges, ensuring they don't exceed viewport
  const expandedRight = Math.min(
    viewportWidth,
    boundingBox.x + boundingBox.width + expansionPixels,
  );
  const expandedBottom = Math.min(
    viewportHeight,
    boundingBox.y + boundingBox.height + expansionPixels,
  );

  // Calculate actual right and bottom expansions
  const actualRightExpansion = expandedRight - (boundingBox.x + boundingBox.width);
  const actualBottomExpansion = expandedBottom - (boundingBox.y + boundingBox.height);

  // Calculate new width and height
  const expandedWidth = expandedRight - expandedX;
  const expandedHeight = expandedBottom - expandedY;

  // Record which sides were fully expanded
  const expansionLimits = {
    left: actualLeftExpansion >= expansionPixels,
    right: actualRightExpansion >= expansionPixels,
    top: actualTopExpansion >= expansionPixels,
    bottom: actualBottomExpansion >= expansionPixels,
  };

  return {
    expandedBox: {
      x: expandedX,
      y: expandedY,
      width: expandedWidth,
      height: expandedHeight,
    },
    expansionLimits,
  };
}
