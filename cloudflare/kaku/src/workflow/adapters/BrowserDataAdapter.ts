import { <PERSON><PERSON> } from 'hono/utils/cookie';

/**
 * Browser session information returned by RemoteBrowserService
 */
export interface BrowserSession {
  wsEndpoint: string;
  sessionId?: string;
}

/**
 * Abstraction for remote browser connection services
 * This interface decouples business logic from specific browser providers (local Chrome, Hyperbrowser, etc.)
 */
export interface RemoteBrowserService {
  /**
   * Create a new browser session
   * @param options - Optional configuration for the browser session
   */
  createSession(options?: {
    browserArgs?: string[];
    device?: ('desktop' | 'mobile')[];
    solveCaptchas?: boolean;
  }): Promise<BrowserSession>;

  /**
   * Get an existing browser session by ID
   * @param sessionId - The session ID to retrieve
   */
  getSession(sessionId: string): Promise<BrowserSession>;

  /**
   * Close a browser session
   * @param sessionId - The session ID to close
   */
  closeSession?(sessionId: string): Promise<void>;
}

/**
 * Abstraction for browser data operations (cookies, localStorage, sessionStorage)
 * This interface decouples business logic from CDP protocol details
 */
export interface BrowserDataAdapter {
  /**
   * Extract all cookies from the current page
   */
  getCookies(): Promise<Cookie[]>;

  /**
   * Extract all localStorage data from the current page
   */
  getLocalStorageData(): Promise<Record<string, string | null>>;

  /**
   * Extract all sessionStorage data from the current page
   */
  getSessionStorageData(): Promise<Record<string, string | null>>;

  /**
   * Set cookies on the current page
   */
  setCookies(cookies: Cookie[]): Promise<void>;

  /**
   * Set localStorage data on the current page
   */
  setLocalStorageData(data: Record<string, string | null>): Promise<void>;

  /**
   * Set sessionStorage data on the current page
   */
  setSessionStorageData(data: Record<string, string | null>): Promise<void>;
}
