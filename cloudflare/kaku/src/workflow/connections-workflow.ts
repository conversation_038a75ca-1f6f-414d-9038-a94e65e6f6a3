import OpenAI from 'openai';
import { CDP } from '../browser/simple-cdp';
import { WorkflowEntrypoint, WorkflowStep, type WorkflowEvent } from 'cloudflare:workers';
import {
  instructions,
  platformLoginLinks,
  defaultWorkflowNoRetryConfig,
  defaultWorkflowRetryConfig,
  K_CUSTOM_VIEWPORT,
} from './utils/constants';
import { extractFormJSON } from './utils/helpers';
import { Environment } from '../common/types';
import {
  ConnectionsWorkflowParams,
  FormSubmissionEventPayload,
} from './types/ConnectionsWorkflowParams';
import { Action, PageStateResult } from '../agent/types/extract-result';
import { decryptData, getEncryptionKey } from '../common/utils';
import {
  initRTCSteaming,
  initScreenCropper,
  initBrowserController,
  injectScript,
  getHashedScriptUrl,
  initTensorFlowDetector,
  injectTensorFlowJS,
  setupInputFocusListener,
} from '../browser';
import {
  ConnectionWorkflowState,
  storeConnectionScreenshotToR2,
} from '../common/utils/storeConnectionScreenshotToR2';
import { PlatformTypes } from '../ui/constants';
import { CDPBrowserDataAdapter } from './adapters/CDPBrowserDataAdapter';
import { BrowserStateService } from './BrowserStateService';
import { R2BrowserStateRepository } from './R2BrowserStateRepository';
import { BrowserServiceFactory, RemoteBrowserService } from './services';
import { raceLLMCalls } from '../llm/llm-call';
import { testLoginCaptcha1 } from '../mock';

export class ConnectionsWorkflow extends WorkflowEntrypoint<
  Environment,
  ConnectionsWorkflowParams
> {
  private cdp?: CDP;

  //target tab props
  private targetSessionId?: string;
  private targetId?: string;

  //control tab props
  private controlTabSessionId?: string;
  private controlTabTargetId?: string;

  private browserStateService: BrowserStateService = new BrowserStateService(
    new R2BrowserStateRepository(this.env.SCREENSHOTS_INBOUND_BUCKET),
  );
  private browserService: RemoteBrowserService = BrowserServiceFactory.createFromEnvironment(
    this.env,
  );

  async run(event: WorkflowEvent<ConnectionsWorkflowParams>, step: WorkflowStep) {
    const browserWsEndpoint = await this.setupBrowserSession(event.payload.sessionId);
    await step.do('setup viewport and device metrics', defaultWorkflowNoRetryConfig, async () => {
      await this.ensureViewportSettings();
      await setupInputFocusListener(this.cdp!, this.targetSessionId);
    });
    await step.do(
      'Initialize session and capture screenshot',
      defaultWorkflowNoRetryConfig,
      async () => this.navigateToLoginPage(event.payload.platformId),
    );
    // Step 2: screenshot
    const screenshot = await step.do(
      'Initialize session and capture screenshot',
      defaultWorkflowNoRetryConfig,
      async () => {
        return await this.captureScreenshot();
      },
    );
    let isCompleted = false;
    let captchaScreenUpdated = false;
    let latestScreenshot = screenshot;

    while (!isCompleted) {
      // Step 3: Generate form with OpenAI
      let formData = await step.do(
        'Generate form with OpenAI',
        defaultWorkflowRetryConfig,
        async () => {
          return await this.generateFormWithOpenAI(latestScreenshot);
        },
      );
      if (formData.pageType === 'loading') {
        console.log('→ Page is loading, waiting for completion and capturing new screenshot');

        const newScreenshot = await step.do(
          'Capture screenshot after loading',
          defaultWorkflowNoRetryConfig,
          async () => await this.captureScreenshot(),
        );

        latestScreenshot = newScreenshot;
        console.log('✓ Page loading completed, screenshot updated');
        continue;
      }
      if (formData.pageType === 'captcha') {
        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.OnCaptcha,
          latestScreenshot,
        );

        const executionContextId = await step.do(
          'Inject scripts for two-tab architecture',
          defaultWorkflowRetryConfig,
          async () =>
            await this.injectScriptsForTwoTabArchitecture(
              browserWsEndpoint,
              event.payload.userId,
              event.payload.platformId,
            ),
        );
        let isCaptchaSolved = false;
        while (!isCaptchaSolved) {
          const viewport = { width: 1024, height: 768 };

          await step.do(
            'Send captcha detection to agent',
            defaultWorkflowNoRetryConfig,
            async () => {
              const { cdp, env } = this;

              if (!cdp) throw new Error('CDP client not initialized');

              const agentName = `${event.payload.userId}:${event.payload.platformId}`;
              const agentId = this.env.Connections.idFromName(agentName);
              const agent = this.env.Connections.get(agentId);
              if (captchaScreenUpdated) {
                // await agent.resumeInteractivity();
              } else {
                await initRTCSteaming(
                  { cdpSession: cdp },
                  `${env.KAKU_WS_ENDPOINT}/agents/connections/${agentName}`,
                  executionContextId,
                  this.targetSessionId!,
                );
              }
              await agent.handleCaptchaDetected(executionContextId, viewport);
            },
          );

          console.log('Waiting for captcha solved notification');
          const captchaSolvedEvent = await step.waitForEvent<{
            differencePercentage: number;
            timestamp: string;
          }>('Await captcha solved', {
            type: 'captcha-solved',
            timeout: '15 minutes',
          });

          console.log(
            'Received captcha solved event',
            captchaSolvedEvent.payload.differencePercentage,
          );

          const newScreenshot = await this.captureScreenshot();

          await step.do('pause interactivity', defaultWorkflowRetryConfig, async () => {
            console.log('✓ interactivity paused');
            const agentName = `${event.payload.userId}:${event.payload.platformId}`;
            const agentId = this.env.Connections.idFromName(agentName);
            const agent = this.env.Connections.get(agentId);
            await agent.pauseInteractivity();
          });

          formData = await step.do(
            'Verify captcha status',
            defaultWorkflowRetryConfig,
            async () => {
              return await this.generateFormWithOpenAI(newScreenshot, true);
            },
          );
          if (formData.pageType !== 'captcha') {
            console.log('✓ Captcha solved, continuing with flow');
            isCaptchaSolved = true;

            const agentName = `${event.payload.userId}:${event.payload.platformId}`;
            const agentId = this.env.Connections.idFromName(agentName);
            const agent = this.env.Connections.get(agentId);
            await agent.handleCaptchaSolved();

            latestScreenshot = newScreenshot;
          } else {
            captchaScreenUpdated = true;
            console.log('Captcha still active, continuing to monitor');
          }
        }
      }

      // Step 4: Acknowledge extracted form
      await step.do('Acknowledge extracted form', defaultWorkflowNoRetryConfig, async () => {
        await this.acknowledgeExtractedForm(
          `${event.payload.userId}:${event.payload.platformId}`,
          formData,
        );
      });

      if (formData.pageType === 'authenticated') {
        console.log('✓ Workflow completed: user authenticated');
        isCompleted = true;
        if (this.cdp) {
          const browserDataAdapter = new CDPBrowserDataAdapter(this.cdp, this.targetSessionId);
          await this.browserStateService.updateBrowserState(
            browserDataAdapter,
            event.payload.userId,
            event.payload.platformId,
          );
        }
        await this.cleanupResources();

        //Save the screenshot to R2
        storeConnectionScreenshotToR2(
          this.env.SCREENSHOTS_INBOUND_BUCKET,
          event.payload.userId,
          event.payload.platformId,
          event.payload.sessionId,
          ConnectionWorkflowState.Authenticated,
          latestScreenshot,
        );

        break;
      }

      // Step 5: Wait for user form input
      const formSubmissionEvent = await step.waitForEvent<string>('Await user form input', {
        type: 'form-submission',
        timeout: '2 minutes',
      });
      console.log('✓ Received form submission event');

      // Step 6: Fill form in browser
      await step.do('Fill form in browser', defaultWorkflowNoRetryConfig, async () => {
        return await this.processFormSubmission(formSubmissionEvent.payload);
      });

      const screenshot = await step.do(
        'Take screenshot',
        defaultWorkflowNoRetryConfig,
        async () => {
          return await this.captureScreenshot();
        },
      );

      latestScreenshot = screenshot;

      //Save the screenshot to R2
      storeConnectionScreenshotToR2(
        this.env.SCREENSHOTS_INBOUND_BUCKET,
        event.payload.userId,
        event.payload.platformId,
        event.payload.sessionId,
        ConnectionWorkflowState.UserFormFilled,
        latestScreenshot,
      );
    }
  }

  private async setupBrowserSession(sessionId: string): Promise<string> {
    const browserSession = await this.browserService.getSession(sessionId);
    const wsEndpoint = browserSession.wsEndpoint;

    this.cdp = new CDP({ webSocketDebuggerUrl: wsEndpoint });

    try {
      await this.cdp.Target.setAutoAttach({
        autoAttach: true,
        flatten: true,
        waitForDebuggerOnStart: false,
      });

      // Set up two-tab architecture
      await this.setupTwoTabArchitecture();

      console.log('✓ Two-tab browser session setup complete');
      console.log('  - Control tab sessionId:', this.controlTabSessionId);
      console.log('  - Target tab sessionId:', this.targetSessionId);
    } catch (error) {
      console.error('Failed to set up browser session:', error);
      throw error;
    }

    return wsEndpoint;
  }

  /**
   * Sets up the two-tab architecture with control and target tabs
   */
  private async setupTwoTabArchitecture(): Promise<void> {
    if (!this.cdp) throw new Error('CDP client not initialized');

    console.log('→ Setting up two-tab architecture');

    // Track attached targets
    const attachedTargets: Array<{ sessionId: string; targetId: string; type: string }> = [];

    // Set up event listener for target attachment
    const targetAttachedPromise = new Promise<void>((resolve) => {
      const handler = ({ params }: { params: any }) => {
        const { sessionId, targetInfo } = params;
        if (targetInfo.type === 'page') {
          attachedTargets.push({
            sessionId,
            targetId: targetInfo.targetId,
            type: targetInfo.type,
          });

          console.log(`→ Target attached: ${targetInfo.targetId} (session: ${sessionId})`);

          // When we have both tabs, assign them appropriately
          if (attachedTargets.length === 2) {
            // First tab becomes control tab, second becomes target tab
            this.controlTabSessionId = attachedTargets[0].sessionId;
            this.controlTabTargetId = attachedTargets[0].targetId;
            this.targetSessionId = attachedTargets[1].sessionId;
            this.targetId = attachedTargets[1].targetId;

            this.cdp?.Target.removeEventListener('attachedToTarget', handler);
            resolve();
          }
        }
      };
      this.cdp?.Target.addEventListener('attachedToTarget', handler);
    });

    // Create control tab first
    console.log('→ Creating control tab');
    await this.cdp.Target.createTarget({ url: 'about:blank' });

    // Create target tab
    console.log('→ Creating target tab');
    await this.cdp.Target.createTarget({ url: 'about:blank' });

    // Wait for both targets to be attached
    await targetAttachedPromise;

    // Enable required domains for both tabs
    await this.enableDomainsForBothTabs();

    console.log('✓ Two-tab architecture setup complete');
  }

  /**
   * Enable required CDP domains for both control and target tabs
   */
  private async enableDomainsForBothTabs(): Promise<void> {
    if (!this.cdp || !this.controlTabSessionId || !this.targetSessionId) {
      throw new Error('CDP client or session IDs not initialized');
    }

    console.log('→ Enabling CDP domains for both tabs');

    // Enable domains for control tab
    await Promise.all([
      this.cdp.Page.enable(undefined, this.controlTabSessionId),
      this.cdp.Runtime.enable(undefined, this.controlTabSessionId),
    ]);

    // Enable domains for target tab
    await Promise.all([
      this.cdp.Page.enable(undefined, this.targetSessionId),
      this.cdp.Runtime.enable(undefined, this.targetSessionId),
    ]);

    // Disable content security policy for both tabs
    await Promise.all([
      this.cdp.Page.setBypassCSP({ enabled: true }, this.controlTabSessionId),
      this.cdp.Page.setBypassCSP({ enabled: true }, this.targetSessionId),
    ]);

    console.log('✓ CDP domains enabled for both tabs');
  }

  private async navigateToLoginPage(platformId: PlatformTypes): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    // goto github.com on controlTapSessionId
    const pageLoadControlTab = this.waitForPageLoad();
    await this.cdp.Page.navigate(
      {
        url: platformLoginLinks[platformId],
      },
      this.controlTabSessionId,
    );
    await pageLoadControlTab;

    console.log(`→ Navigating to ${platformId}`);
    const pageLoad = this.waitForPageLoad();

    await this.cdp.Page.navigate(
      {
        url: platformLoginLinks[platformId],
      },
      this.targetSessionId,
    );

    await pageLoad;
    console.log('✓ Page loaded');

    console.log('✓ Navigation completed and viewport configured');
  }

  private async waitForPageLoad(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return Promise.resolve();
    await new Promise<void>((resolve) => {
      const handler = () => {
        this.cdp?.Page.removeEventListener('loadEventFired', handler);
        resolve();
      };
      this.cdp?.Page.addEventListener('loadEventFired', handler);
    });
  }

  /**
   * Ensures the viewport and device metrics are set to the correct dimensions
   * This should be called after navigation or any time we need to guarantee viewport consistency
   */
  private async ensureViewportSettings(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    console.log('→ Ensuring viewport settings are correct (1024x768)');

    await this.cdp.Emulation.setDeviceMetricsOverride(
      {
        //store these to constants
        width: K_CUSTOM_VIEWPORT.width,
        height: K_CUSTOM_VIEWPORT.height,
        deviceScaleFactor: 1,
        mobile: false,
      },
      this.targetSessionId,
    );

    console.log('✓ Viewport settings confirmed');
  }

  private async captureScreenshot(): Promise<string> {
    if (!this.cdp || !this.targetSessionId) return '';

    console.log('→ Capturing screenshot');
    const screenshot = await this.cdp.Page.captureScreenshot(
      {
        format: 'webp',
        captureBeyondViewport: false,
      },
      this.targetSessionId,
    );

    const sizeKB = (screenshot.data.length * 0.75) / 1024;
    console.log(`✓ Screenshot captured: ${sizeKB.toFixed(2)} KB`);
    // console.log screenshot dimensions
    const dimensions = await this.cdp.Page.getLayoutMetrics(undefined, this.targetSessionId);
    console.log(
      `Screenshot dimensions: ${dimensions.contentSize.width} x ${dimensions.contentSize.height}`,
    );

    return screenshot.data;
  }

  /**
   * Inject scripts for two-tab architecture
   * Control tab gets CDP management scripts, target tab gets user interaction scripts
   */
  private async injectScriptsForTwoTabArchitecture(
    browserWsEndpoint: string,
    userId: string,
    platformId: string,
  ): Promise<number> {
    if (!this.cdp || !this.targetSessionId || !this.controlTabSessionId)
      throw new Error('CDP client or session IDs not initialized');

    // Extract main frame IDs for both tabs
    const targetFrameTree = await this.cdp.Page.getFrameTree(undefined, this.targetSessionId);
    const controlFrameTree = await this.cdp.Page.getFrameTree(undefined, this.controlTabSessionId);

    const targetMainFrameId = targetFrameTree.frameTree.frame.id;
    const controlMainFrameId = controlFrameTree.frameTree.frame.id;

    // Create isolated worlds for both tabs
    const { executionContextId: targetExecutionContextId } =
      await this.cdp.Page.createIsolatedWorld(
        {
          frameId: targetMainFrameId,
          worldName: 'kaku-target-world',
          grantUniveralAccess: true,
        },
        this.targetSessionId,
      );

    const { executionContextId: controlExecutionContextId } =
      await this.cdp.Page.createIsolatedWorld(
        {
          frameId: controlMainFrameId,
          worldName: 'kaku-control-world',
          grantUniveralAccess: true,
        },
        this.controlTabSessionId,
      );

    // Inject scripts in parallel for both tabs
    await Promise.all([
      this.injectControlTabScripts(browserWsEndpoint, controlExecutionContextId),
      this.injectTargetTabScripts(browserWsEndpoint, userId, platformId, targetExecutionContextId),
    ]);

    return targetExecutionContextId;
  }

  /**
   * Inject scripts for the control tab (CDP management)
   */
  private async injectControlTabScripts(
    browserWsEndpoint: string,
    executionContextId: number,
  ): Promise<void> {
    if (!this.cdp || !this.controlTabSessionId || !this.controlTabTargetId) {
      throw new Error('Control tab not properly initialized');
    }

    console.log('→ Injecting control tab scripts');

    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const persistentCDPControllerUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'persistent-cdp-controller.min.js',
    );

    try {
      // Inject cross-tab communication utility first
      await injectScript(this.cdp, crossTabCommUrl, executionContextId, this.controlTabSessionId);

      // Inject persistent CDP controller
      await injectScript(
        this.cdp,
        persistentCDPControllerUrl,
        executionContextId,
        this.controlTabSessionId,
      );

      // Initialize the persistent CDP controller
      // targetId is passed to attach and allow controlling of the target tab
      const initScript = `
        (async () => {
          await window.persistentCDPController.init(
            '${browserWsEndpoint}',
            '${this.targetId}'
          );
        })();
      `;

      await this.cdp.Runtime.evaluate(
        {
          expression: initScript,
          contextId: executionContextId,
          awaitPromise: true,
        },
        this.controlTabSessionId,
      );
    } catch (error) {
      console.error('Error injecting control tab scripts:', error);
      throw error;
    }
  }

  /**
   * Inject scripts for the target tab (user interaction)
   */
  private async injectTargetTabScripts(
    browserWsEndpoint: string,
    userId: string,
    platformId: string,
    executionContextId: number,
  ): Promise<void> {
    if (!this.cdp || !this.targetSessionId) {
      throw new Error('Target tab not properly initialized');
    }

    console.log('→ Injecting target tab scripts');

    // Get script URLs
    const crossTabCommUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'cross-tab-communicator.min.js',
    );

    const browserControllerProxyUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'browser-controller-proxy.min.js',
    );

    const screenCropperUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screen-cropper.min.js',
    );

    const captchaDetectorUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector.min.js',
    );

    const screenshotComparisonUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'screenshot-comparison.min.js',
    );

    const captchaDetectorTfUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'captcha-detector-tf.min.js',
    );

    const tfModelBundleUrl = getHashedScriptUrl(
      this.env.KAKU_API_ENDPOINT,
      'tf-model-bundle.min.js',
    );

    try {
      const layoutMetrics = await this.cdp.Page.getLayoutMetrics(undefined, this.targetSessionId);
      const viewport = {
        width: layoutMetrics.cssLayoutViewport.clientWidth,
        height: layoutMetrics.cssLayoutViewport.clientHeight,
      };

      // Group 1: Inject core scripts in parallel
      console.log('→ Injecting target tab core scripts...');
      await Promise.all([
        injectScript(this.cdp, crossTabCommUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, browserControllerProxyUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, screenshotComparisonUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, screenCropperUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, captchaDetectorUrl, executionContextId, this.targetSessionId),
        injectTensorFlowJS(this.cdp, executionContextId, this.targetSessionId),
      ]);

      // Group 2: Initialize proxy browser controller first (screen cropper depends on it)
      console.log('→ Initializing proxy browser controller...');
      const initScript = `
        (async () => {
          await window.browserController.init();
        })();
      `;
      await this.cdp.Runtime.evaluate(
        {
          expression: initScript,
          contextId: executionContextId,
          awaitPromise: true,
        },
        this.targetSessionId,
      );
      console.log('✓ Proxy browser controller initialized');

      // Group 3: Inject TensorFlow-dependent scripts and initialize screen cropper
      console.log('→ Initializing screen cropper and TensorFlow scripts...');
      await Promise.all([
        injectScript(this.cdp, captchaDetectorTfUrl, executionContextId, this.targetSessionId),
        injectScript(this.cdp, tfModelBundleUrl, executionContextId, this.targetSessionId),
        initScreenCropper(
          this.cdp,
          `${this.env.KAKU_WS_ENDPOINT}/agents/connections/${userId}:${platformId}`,
          executionContextId,
          { width: viewport.width, height: viewport.height },
          this.targetSessionId,
        ),
      ]);

      // Group 4: Initialize TensorFlow detector
      console.log('→ Initializing TensorFlow detector...');
      await initTensorFlowDetector(this.cdp, executionContextId, viewport, this.targetSessionId);

      console.log('✓ Target tab scripts injected and initialized');
    } catch (error) {
      console.error('Error injecting target tab scripts:', error);
      throw error;
    }
  }

  private async generateFormWithOpenAI(
    screenshot: string,
    bigDiff: boolean = false,
  ): Promise<PageStateResult> {
    const client = new OpenAI({
      apiKey: this.env.OPENAI_API_KEY,
      baseURL: this.env.AI_GATEWAY_URL,
    });
    const layoutMetrics = await this.cdp!.Page.getLayoutMetrics(undefined, this.targetSessionId);
    const viewPort = {
      width: layoutMetrics.cssLayoutViewport.clientWidth,
      height: layoutMetrics.cssLayoutViewport.clientHeight,
    };

    const response = await raceLLMCalls({
      client,
      screenshot,
      prompt: instructions,
      viewportWidth: viewPort?.width,
      viewportHeight: viewPort?.height,
      noOfConcurrentCalls: 2,
      enable: this.env.ENVIRONMENT !== 'local',
    });
    const end = Date.now();
    return extractFormJSON(response.result.output_text);
  }

  private async acknowledgeExtractedForm(connectionDOName: string, formData: any): Promise<void> {
    const agent = this.env.Connections.idFromName(connectionDOName);
    const stub = this.env.Connections.get(agent);

    console.log('→ Sending form to Durable Object');
    await stub.onFormStateChange({
      extractedData: formData,
    });
  }

  private async processFormSubmission(formSubmissionPayload: string): Promise<void> {
    try {
      console.log(
        '→ Starting form fill process with coordinate-based interaction',
        formSubmissionPayload,
      );
      const encryptionKey = await getEncryptionKey();
      const decrypted = await decryptData(formSubmissionPayload, encryptionKey);
      const actionsPayload = JSON.parse(decrypted as string) as FormSubmissionEventPayload;
      console.log(actionsPayload);
      // Sort actions by order, ensuring submit actions are last
      const sortedActions = actionsPayload.actions.sort((a, b) => a.order - b.order);

      // Process each action in order
      for (const action of sortedActions) {
        await this.executeAction(action);
      }

      console.log('→ Waiting for page to update after form submission');
      await this.waitForPageUpdateAfterSubmission();
    } catch (error) {
      console.error(`✖ Error filling form: ${(error as Error).message}`);
      throw error;
    }
  }

  private async executeAction(action: Action): Promise<void> {
    const { type, coordinates, name, value } = action;
    console.log(`→ Processing action "${type}" at (${coordinates.x}, ${coordinates.y})`);

    if (type === 'fill' && value) {
      await this.fillTextField(coordinates, name, value);
    } else if (type === 'click') {
      await this.clickAt(coordinates);
    }

    await sleep(100);
  }

  private async fillTextField(
    coordinates: { x: number; y: number },
    name: string,
    value: string,
  ): Promise<void> {
    try {
      console.log(`→ Focusing element for "${name}" at (${coordinates.x}, ${coordinates.y})`);

      // Click on the field
      await this.clickAt(coordinates);

      // Wait for focus
      await sleep(200);

      // Triple-click to select all text
      await this.tripleClickAt(coordinates);

      // Clear any existing text
      await this.pressBackspace();

      await sleep(200);
      console.log(`→ Typing "${value}" into field "${name}"`);

      // Type the value
      await this.typeText(value);

      console.log(`✓ Successfully entered text in "${name}"`);
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async clickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Mouse down
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );

      // Mouse up to complete click
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: 1,
        },
        this.targetSessionId,
      );
    } catch (error) {
      throw Error(`Error clicking: ${(error as Error).message}`);
    }
  }

  private async tripleClickAt(coordinates: { x: number; y: number }): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    for (let i = 0; i < 3; i++) {
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
      await this.cdp.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: coordinates.x,
          y: coordinates.y,
          button: 'left',
          clickCount: i + 1,
        },
        this.targetSessionId,
      );
      await sleep(50);
    }
  }

  private async pressBackspace(): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyDown',
        windowsVirtualKeyCode: 8,
        key: 'Backspace',
      },
      this.targetSessionId,
    );
    await this.cdp.Input.dispatchKeyEvent(
      {
        type: 'keyUp',
        key: 'Backspace',
      },
      this.targetSessionId,
    );
  }

  private async typeText(text: string): Promise<void> {
    if (!this.cdp || !this.targetSessionId) return;

    try {
      // Set the clipboard content
      await this.cdp.Input.insertText(
        {
          text: text,
        },
        this.targetSessionId,
      );

      console.log(
        `✓ Successfully pasted text: "${text.substring(0, 10)}${text.length > 10 ? '...' : ''}"`,
      );
    } catch (error) {
      throw Error(`✖ Error pasting text: ${(error as Error).message}`);
    }
  }

  private async waitForPageUpdateAfterSubmission(): Promise<void> {
    if (!this.cdp) return;

    const pageLoad = this.waitForPageLoad();
    console.log('→ Waiting for page load');
    await withTimeout(pageLoad, 10000);
    console.log('✓ Page reloaded after submission');
  }

  private async cleanupResources(): Promise<void> {
    if (this.cdp) {
      this.cdp.connection.close();
      this.cdp = undefined;
    }
  }
}

function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<unknown> {
  return Promise.race([
    promise,
    new Promise((resolve) => setTimeout(() => resolve(true), timeoutMs)),
  ]);
}

function sleep(timeInMillis: number): Promise<void> {
  return new Promise((r) => setTimeout(r, timeInMillis));
}
