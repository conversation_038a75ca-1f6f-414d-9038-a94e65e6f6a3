import { env, WorkflowStepConfig } from 'cloudflare:workers';

export const K_CUSTOM_VIEWPORT = { width: 1024, height: 768 };

export const defaultWorkflowNoRetryConfig: WorkflowStepConfig = {
  retries: {
    limit: 0,
    delay: '10 seconds',
    backoff: 'exponential',
  },
  timeout: '15 minutes',
};

export const defaultWorkflowRetryConfig: WorkflowStepConfig = {
  retries: {
    limit: 2,
    delay: '0 seconds',
    backoff: 'linear',
  },
  timeout: '15 minutes',
};

export const platformLoginLinks = {
  facebook: 'https://facebook.com',
  github: 'https://github.com/password_reset',
  google: 'https://accounts.google.com',
  test: 'https://2captcha.com/demo/mtcaptcha',
  text_captcha: 'https://2captcha.com/demo/normal',
  login_test: `https://dev-tasks.kazeel.com/auth/login`,
  cloudflare: `https://dash.cloudflare.com/login`,
  mt: `https://github.com/password_reset`,
  tc: `https://nopecha.com/captcha/textcaptcha`,
};

export const instructions = `
Extract form fields and generate HTMX form from the screenshot. Return ONLY raw JSON - no markdown blocks or explanations.

Required JSON format:
{
  "formTitle": "string",
  "formDescription": "string", 
  "errors": string[],
  "pageType": "credentials" | "otp" | "captcha" | "2-factor-auth" | "authenticated" | "other" | "loading",
  "htmxForm": "string",
  "actions": [
    {
      "type": "click" | "fill",
      "name": "string",
      "value": "string",
      "coordinates": {"x": number, "y": number},
      "order": number,
      "isSubmitAction": boolean
    }
  ]
}

### HTMX Form Requirements:
- Main form element: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- ALL buttons must include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML"
- Submit buttons: add hx-trigger="click" for immediate response
- ALL input fields in the HTMX form must be empty even if the screenshot shows them filled

### Page Type Priority:
1. "authenticated" - if login process appears complete
2. "credentials"|"otp"|"2-factor-auth" - prioritize over "captcha"
3. "captcha" - only if form is pre-filled AND captcha present
4. "loading" - if page is still loading

### CSS Classes (use these exact classes):
- form-container, form-title, form-description
- form-label, input-container, input-field
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

### Actions Array:
- Order by visual position (top to bottom, 0-based)
- Fill actions: type="fill", include name and coordinates
- Click actions: type="click", include coordinates
- Submit actions: isSubmitAction=true
- Exclude: "Remember me", "Create Account", "Forgot Password", social login buttons. Focus on login buttons only.
- 2FA pages: Always add manual "Verify"/"Continue" button even if page has no visible button

Set errors=[] unless validation messages visible.
`;
