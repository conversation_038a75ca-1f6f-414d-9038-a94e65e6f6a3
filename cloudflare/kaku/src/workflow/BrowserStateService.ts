import { <PERSON>rowserState } from './types/BrowserState';
import { BrowserStateRepository } from './types/BrowserStateRepository';
import { BrowserDataAdapter } from './adapters/BrowserDataAdapter';

export class BrowserStateService {
  browserStateRepository: BrowserStateRepository;

  constructor(browserStateRepository: BrowserStateRepository) {
    this.browserStateRepository = browserStateRepository;
  }

  async getBrowserState(userId: string, platform: string): Promise<BrowserState | null> {
    return await this.browserStateRepository.getBrowserState(userId, platform);
  }

  async updateBrowserState(
    browserDataAdapter: BrowserDataAdapter,
    userId: string,
    platform: string,
  ): Promise<BrowserState> {
    const cookies = await browserDataAdapter.getCookies();
    const localStorageData = await browserDataAdapter.getLocalStorageData();
    const sessionStorageData = await browserDataAdapter.getSessionStorageData();
    return await this.browserStateRepository.updateBrowserState({
      userId,
      platform,
      cookies,
      localStorageData,
      sessionStorageData,
    });
  }

  async deleteBrowserState(userId: string, platform: string): Promise<void> {
    await this.browserStateRepository.deleteBrowserState(userId, platform);
  }

  async loadBrowserStateToPage(
    browserDataAdapter: BrowserDataAdapter,
    userId: string,
    platform: string,
  ): Promise<void> {
    const browserState = await this.getBrowserState(userId, platform);

    if (browserState) {
      await browserDataAdapter.setCookies(browserState.cookies);
      await browserDataAdapter.setLocalStorageData(browserState.localStorageData);
      await browserDataAdapter.setSessionStorageData(browserState.sessionStorageData);
    }
  }
}
