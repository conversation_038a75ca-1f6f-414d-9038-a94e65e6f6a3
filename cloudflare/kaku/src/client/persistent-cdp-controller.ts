import Protocol from 'devtools-protocol';
import { CDP } from '../browser/simple-cdp';
import { CrossTabCommunicator } from './utils/cross-tab-communicator';

declare global {
  interface Window {
    persistentCDPController: any;
  }
}

/**
 * Persistent CDP Controller - handles all CDP operations
 * Runs in a dedicated control tab to maintain persistent CDP connection
 * Communicates with target tab via cross-tab messaging
 */
(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;
  let communicator: CrossTabCommunicator | null = null;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[PersistentCDPController]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[PersistentCDPController]', ...args);
  }

  /**
   * Initialize the persistent CDP controller
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   */
  async function init(browserFullWsEndpoint: string, targetId: string): Promise<void> {
    log('Initializing persistent CDP controller');
    log('Connecting to CDP and attaching to target:', targetId);

    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log('CDP connection established with sessionId:', sessionId);

    // Initialize cross-tab communication
    communicator = new CrossTabCommunicator({
      channelName: 'browser-controller',
      debug: config.debug,
      timeout: 15000,
    });

    // Set up message handlers for CDP operations
    setupMessageHandlers();

    log('Persistent CDP controller initialized successfully');
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string,
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log('Attaching to target:', targetId);
      const { sessionId: attachedSessionId } = await cdpClient.Target.attachToTarget({
        targetId,
        flatten: true,
      });

      sessionId = attachedSessionId;
      log('Successfully attached to target with sessionId:', sessionId);

      // Enable necessary domains
      await Promise.all([
        cdpClient.Runtime.enable(undefined, sessionId),
        cdpClient.Page.enable(undefined, sessionId),
      ]);
      await cdpClient.Page.setBypassCSP({ enabled: true }, sessionId);

      log('CDP domains enabled successfully');
    } catch (err) {
      error('Failed to connect to CDP or attach to target:', err);
      throw err;
    }
  }

  /**
   * Set up message handlers for cross-tab communication
   */
  function setupMessageHandlers(): void {
    if (!communicator) {
      throw new Error('Communicator not initialized');
    }

    communicator.onMessage(async (message) => {
      log('Received message from target tab:', message.type, message.data);

      switch (message.type) {
        case 'ping':
          return { success: true, message: 'pong from persistent CDP controller' };

        case 'takeScreenshot':
          return await takeScreenshot();

        case 'dispatchMouseMove':
          return await dispatchMouseMove(message.data.x, message.data.y);

        case 'dispatchMouseDown':
          return await dispatchMouseDown(message.data.x, message.data.y, message.data.button);

        case 'dispatchMouseUp':
          return await dispatchMouseUp(message.data.x, message.data.y, message.data.button);

        case 'dispatchMouseClick':
          return await dispatchMouseClick(message.data.x, message.data.y, message.data.button);

        case 'dispatchKeyEvent':
          return await dispatchKeyEvent(message.data.type, message.data.key, message.data.code);

        case 'insertText':
          return await insertText(message.data.text);

        case 'setupBrowserMetrics':
          return await setupBrowserMetrics(message.data.viewport);

        case 'requestNewFrame':
          return await requestNewFrame();

        case 'triggerMouseMovement':
          return await triggerMouseMovement();

        case 'getPageInfo':
          return await getPageInfo();

        case 'handleInputEvent':
          return await handleInputEvent(message.data.event);

        default:
          throw new Error(`Unknown message type: ${message.type}`);
      }
    });

    log('Message handlers set up successfully');
  }

  /**
   * Take a screenshot using CDP
   */
  async function takeScreenshot(): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      log('Taking screenshot via CDP');
      const result = await cdpClient.Page.captureScreenshot(
        {
          format: 'png',
          quality: 90,
          captureBeyondViewport: false,
        },
        sessionId,
      );

      log('Screenshot captured successfully');
      return {
        success: true,
        data: result.data,
        timestamp: Date.now(),
      };
    } catch (err) {
      error('Failed to take screenshot:', err);
      throw err;
    }
  }

  /**
   * Dispatch mouse move event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseMoved',
          x: x,
          y: y,
        },
        sessionId,
      );

      log(`Mouse moved to (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      error('Failed to dispatch mouse move:', err);
      throw err;
    }
  }

  /**
   * Dispatch mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );

      isMouseDown = true;
      log(`Mouse down at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      error('Failed to dispatch mouse down:', err);
      throw err;
    }
  }

  /**
   * Dispatch mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );

      isMouseDown = false;
      log(`Mouse up at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      error('Failed to dispatch mouse up:', err);
      throw err;
    }
  }

  /**
   * Dispatch mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    try {
      await dispatchMouseDown(x, y, button);
      await new Promise((resolve) => setTimeout(resolve, 50));
      await dispatchMouseUp(x, y, button);

      log(`Mouse clicked at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      error('Failed to dispatch mouse click:', err);
      throw err;
    }
  }

  /**
   * Dispatch key event
   */
  async function dispatchKeyEvent(
    type: 'keyDown' | 'keyUp' | 'char',
    key: string,
    code?: string,
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Input.dispatchKeyEvent(
        {
          type: type,
          key: key,
          code: code,
        },
        sessionId,
      );

      log(`Key event dispatched: ${type} ${key}`);
      return { success: true };
    } catch (err) {
      error('Failed to dispatch key event:', err);
      throw err;
    }
  }

  /**
   * Insert text
   */
  async function insertText(text: string): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Input.insertText({ text }, sessionId);
      log(`Text inserted: ${text}`);
      return { success: true };
    } catch (err) {
      error('Failed to insert text:', err);
      throw err;
    }
  }

  /**
   * Setup browser metrics
   */
  async function setupBrowserMetrics(viewport: { width: number; height: number }): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    const { windowId } = await cdpClient.Browser.getWindowForTarget({}, sessionId);
    await cdpClient.Browser.setWindowBounds(
      {
        windowId,
        bounds: {
          width: viewport.width,
          height: viewport.height + 75,
        },
      },
      sessionId,
    );
    try {
      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: viewport.width,
          height: viewport.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );

      log('Browser metrics set up successfully');
      return { success: true };
    } catch (err) {
      error('Failed to setup browser metrics:', err);
      throw err;
    }
  }

  /**
   * Request new frame using opacity technique
   */
  async function requestNewFrame(): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      await cdpClient.Runtime.evaluate(
        {
          expression: `
            (async () => {
              try {
                const body = document.body || document.documentElement;
                const originalOpacity = body.style.opacity;

                body.style.opacity = '0.9999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = '0.99999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = originalOpacity || '';

                return { success: true, technique: 'opacity-change', timestamp: Date.now() };
              } catch (e) {
                return { success: false, error: e.message, timestamp: Date.now() };
              }
            })()
          `,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );

      log('New frame requested successfully');
      return { success: true };
    } catch (err) {
      error('Failed to request new frame:', err);
      throw err;
    }
  }

  /**
   * Trigger mouse movement for frame generation
   */
  async function triggerMouseMovement(): Promise<any> {
    try {
      await dispatchMouseMove(100, 100);
      await new Promise((resolve) => setTimeout(resolve, 16));
      await dispatchMouseMove(101, 101);
      await new Promise((resolve) => setTimeout(resolve, 16));
      await dispatchMouseMove(100, 100);

      log('Mouse movement triggered for frame generation');
      return { success: true };
    } catch (err) {
      error('Failed to trigger mouse movement:', err);
      throw err;
    }
  }

  /**
   * Get page information
   */
  async function getPageInfo(): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      const result = await cdpClient.Runtime.evaluate(
        {
          expression: `
            ({
              url: window.location.href,
              title: document.title,
              readyState: document.readyState,
              timestamp: Date.now()
            })
          `,
          returnByValue: true,
        },
        sessionId,
      );

      log('Page info retrieved successfully');
      return {
        success: true,
        data: result.result.value,
      };
    } catch (err) {
      error('Failed to get page info:', err);
      throw err;
    }
  }

  /**
   * Handle input event (comprehensive event handling)
   */
  async function handleInputEvent(event: any): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new Error('CDP client not initialized');
    }

    try {
      log('Handling input event:', event.type, event);

      switch (event.type) {
        case 'mousedown':
          await dispatchMouseDown(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;
        case 'mouseup':
          await dispatchMouseUp(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;
        case 'mousemove':
          await dispatchMouseMove(event.x, event.y);
          break;
        case 'click':
          await dispatchMouseClick(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;

        // Character-based input handling
        case 'char-input':
          log(
            '✅ [CHAR-INPUT] Processing character:',
            event.text,
            'from:',
            event.source || 'unknown',
          );
          if (event.text !== undefined) {
            // Handle special characters
            if (event.text === '\b' || event.inputType === 'deleteContentBackward') {
              // Backspace
              await dispatchKeyEvent('keyDown', 'Backspace');
              await dispatchKeyEvent('keyUp', 'Backspace');
            } else if (event.text === '\x7F' || event.inputType === 'deleteContentForward') {
              // Delete
              await dispatchKeyEvent('keyDown', 'Delete');
              await dispatchKeyEvent('keyUp', 'Delete');
            } else if (
              event.text === '\n' ||
              event.inputType === 'insertLineBreak' ||
              event.inputType === 'insertParagraph'
            ) {
              // Enter/newline
              await dispatchKeyEvent('keyDown', 'Enter');
              await dispatchKeyEvent('keyUp', 'Enter');
            } else if (event.text && event.text.length > 0) {
              // Regular character input
              await insertText(event.text);
              log('✅ [CHAR-INPUT] Successfully inserted text:', event.text);
            } else {
              log('⚠️ [CHAR-INPUT] Empty character input, likely handled as special key');
            }
          } else {
            log('⚠️ [CHAR-INPUT] Warning: Undefined character input received');
          }
          break;

        case 'text-insert':
          log(
            '✅ [TEXT-INSERT] Processing text insertion:',
            event.text,
            'from:',
            event.source || 'unknown',
          );
          if (event.text && event.text.length > 0) {
            // Sanitize text to remove control characters
            const sanitizedText = event.text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
            if (sanitizedText.length > 0) {
              await insertText(sanitizedText);
              log('✅ [TEXT-INSERT] Successfully inserted sanitized text:', sanitizedText);
            } else {
              log('⚠️ [TEXT-INSERT] Warning: Text became empty after sanitization');
            }
          } else {
            log('⚠️ [TEXT-INSERT] Warning: Empty text insertion received');
          }
          break;

        case 'navigation-key':
          log('✅ [NAVIGATION-KEY] Processing navigation key:', event.key);
          // Handle pure navigation keys (arrows, function keys, etc.)
          await dispatchKeyEvent('keyDown', event.key, event.code);
          await dispatchKeyEvent('keyUp', event.key, event.code);
          break;

        // Legacy keyboard event handling (kept for compatibility)
        case 'keydown':
          log(
            '🔄 [LEGACY-KEYDOWN] Processing keydown:',
            event.key,
            'from:',
            event.source || 'unknown',
          );
          await dispatchKeyEvent('keyDown', event.key, event.code);
          break;
        case 'keyup':
          log('🔄 [LEGACY-KEYUP] Processing keyup:', event.key, 'from:', event.source || 'unknown');
          await dispatchKeyEvent('keyUp', event.key, event.code);
          break;
        case 'keypress':
          log(
            '🔄 [LEGACY-KEYPRESS] Processing keypress:',
            event.key,
            'from:',
            event.source || 'unknown',
          );
          await dispatchKeyEvent('char', event.key, event.code);
          break;
        default:
          log('Unhandled input event type:', event.type);
          return { success: false, error: `Unhandled input event type: ${event.type}` };
      }

      return { success: true };
    } catch (err) {
      error('Failed to handle input event:', err);
      throw err;
    }
  }

  // Expose public API
  (globalThis as any).persistentCDPController = {
    init,
  };

  log('Persistent CDP controller script loaded');
})();
