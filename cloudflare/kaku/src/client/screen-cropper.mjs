(function () {
  const config = {
    frameRate: 15,
    debug: true,
  };

  let socket = null;
  let pc = null;
  let cropX, cropY, width, height;
  let screenStream = null;
  let cropRegion = {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  };

  // State management for initialization
  let isInitialized = false;

  function log(...args) {
    if (config.debug) {
      console.log('[screenCropper]', ...args);
    }
  }

  function error(...args) {
    console.error('[screenCropper]', ...args);
  }

  /**
   * Initialize screen cropper with WebSocket and WebRTC connections
   * This method sets up the connections but doesn't start streaming
   */
  async function init(wsEndpoint, viewPort) {
    if (isInitialized) {
      log('Screen cropper already initialized');
      return 'ALREADY_INITIALIZED';
    }

    try {
      log('🔧 [init] Starting screen cropper initialization...');
      log('🔧 [init] WebSocket endpoint:', wsEndpoint);
      log('🔧 [init] Viewport:', viewPort);

      // Wait for browser controller to be available before proceeding
      log('🔧 [init] Step 1: Waiting for browser controller...');
      await waitForBrowserController();
      log('✅ [init] Browser controller is ready');

      // Step 1: Setup WebSocket connection
      log('🔧 [init] Step 2: Setting up WebSocket connection...');
      await connectToSocket(wsEndpoint);
      log('✅ [init] WebSocket connection established');

      // Step 2: Setup WebRTC connection with input channel
      log('🔧 [init] Step 3: Setting up WebRTC connection...');
      await connectToWebRTC();
      log('✅ [init] WebRTC connection established');

      isInitialized = true;
      log('✅ [init] Screen cropper initialization completed successfully');
      return 'SUCCESS';
    } catch (err) {
      error('❌ [init] Failed to initialize screen cropper:', err);
      error('❌ [init] Error stack:', err.stack);
      throw err;
    }
  }

  /**
   * Start streaming after initialization is complete
   * This method begins the actual screen capture and streaming
   */
  async function start(viewPort) {
    if (!isInitialized) {
      throw new Error('Screen cropper must be initialized before starting');
    }

    try {
      log('Starting screen cropper streaming');
      log('Viewport:', viewPort);

      log('Getting initial bounding box from TF detector...');
      const cropBox = await window.tfCaptchaDetector.getInitialBoundingBox(viewPort);
      log('Initial crop box:', cropBox);

      cropX = cropBox.x;
      cropY = cropBox.y;
      width = cropBox.width;
      height = cropBox.height;

      log('Starting streaming with crop box...');
      await startStreaming(viewPort, cropBox);
      log('Screen cropper streaming started successfully');
    } catch (err) {
      error('Failed to start screen cropper streaming:', err);
      throw err;
    }
  }

  /**
   * Waits for the browser controller to be available and initialized before proceeding
   */
  async function waitForBrowserController() {
    let attempts = 0;
    const maxAttempts = 50; // 5 seconds max wait

    while (attempts < maxAttempts) {
      // Check if browser controller exists
      if (!window.browserController) {
        log(`Waiting for browser controller... (attempt ${attempts + 1}/${maxAttempts})`);
      } else {
        try {
          log(
            `Browser controller found, testing initialization... (attempt ${attempts + 1}/${maxAttempts})`,
          );
          await window.browserController.ping();
          log('Browser controller is available and initialized');
          return;
        } catch (error) {
          log(
            `Browser controller not yet initialized: ${error.message} (attempt ${attempts + 1}/${maxAttempts})`,
          );
        }
      }

      await new Promise((resolve) => setTimeout(resolve, 100));
      attempts++;
    }

    throw new Error('Browser controller not available or not initialized after waiting');
  }

  /**
   * Connect to WebSocket and wait for it to be ready
   */
  async function connectToSocket(wsEndpoint) {
    return new Promise((resolve, reject) => {
      try {
        log('🔧 [connectToSocket] Connecting to WebSocket:', wsEndpoint);

        if (!wsEndpoint) {
          throw new Error('WebSocket endpoint is required');
        }

        socket = new WebSocket(wsEndpoint);

        // Expose socket to window for captcha detector to use
        window.socket = socket;
        log('✅ [connectToSocket] Socket exposed to window');

        socket.addEventListener('open', () => {
          log('✅ [connectToSocket] WebSocket connected successfully');
          resolve();
        });

        socket.addEventListener('error', (e) => {
          error('❌ [connectToSocket] WebSocket connection error:', e);
          reject(new Error(`WebSocket connection failed: ${e.message || 'Unknown error'}`));
        });

        socket.addEventListener('close', (e) => {
          log('🔌 [connectToSocket] WebSocket connection closed:', e.code, e.reason);
        });

        // Set up message handlers
        setupSocketMessageHandlers();
        log('✅ [connectToSocket] Message handlers set up');
      } catch (err) {
        error('❌ [connectToSocket] Error setting up WebSocket:', err);
        reject(err);
      }
    });
  }

  /**
   * Connect to WebRTC and set up input channel
   */
  async function connectToWebRTC() {
    return new Promise((resolve, reject) => {
      try {
        log('🔧 [connectToWebRTC] Setting up WebRTC peer connection...');

        pc = new RTCPeerConnection({
          iceServers: [
            { urls: 'stun:stun.cloudflare.com:3478' },
            {
              urls: 'turn:relay1.expressturn.com:3478',
              username: 'ef89RMU4SHUQMSOUU9',
              credential: 'jvkMMnQxWX4Qrhe3',
            },
          ],
        });
        log('✅ [connectToWebRTC] RTCPeerConnection created');

        pc.onicecandidate = (event) => {
          if (event.candidate && socket?.readyState === WebSocket.OPEN) {
            log('🔧 [connectToWebRTC] Sending ICE candidate');
            socket.send(JSON.stringify({ type: 'candidate', candidate: event.candidate }));
          }
        };

        // Set up connection state monitoring
        pc.onconnectionstatechange = () => {
          log('🔧 [connectToWebRTC] WebRTC connection state:', pc.connectionState);
          if (pc.connectionState === 'connected') {
            log('✅ [connectToWebRTC] WebRTC connection established successfully');
            resolve();
          } else if (pc.connectionState === 'failed') {
            error('❌ [connectToWebRTC] WebRTC connection failed');
            reject(new Error('WebRTC connection failed'));
          } else if (pc.connectionState === 'disconnected') {
            log('🔌 [connectToWebRTC] WebRTC connection disconnected');
          }
        };

        // Set up ICE connection state monitoring
        pc.oniceconnectionstatechange = () => {
          log('🔧 [connectToWebRTC] ICE connection state:', pc.iceConnectionState);
        };

        log('✅ [connectToWebRTC] WebRTC peer connection setup completed');

        // Don't create input channel immediately - wait for signaling
        // The input channel will be created when we create an offer
        log('✅ [connectToWebRTC] WebRTC setup completed, waiting for signaling...');
        resolve();
      } catch (err) {
        error('❌ [connectToWebRTC] Error setting up WebRTC:', err);
        reject(err);
      }
    });
  }

  /**
   * Set up WebSocket message handlers
   */
  function setupSocketMessageHandlers() {
    socket.addEventListener('message', async (event) => {
      let msg;
      try {
        msg = JSON.parse(event.data);
      } catch (error) {
        return;
        // ignore non json message (possible htmx)
      }
      switch (msg.type) {
        case 'offer':
          await pc.setRemoteDescription(new RTCSessionDescription(msg.offer));
          const answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);
          socket.send(JSON.stringify({ type: 'answer', answer }));
          break;
        case 'answer':
          await pc.setRemoteDescription(new RTCSessionDescription(msg.answer));
          break;
        case 'candidate':
          await pc.addIceCandidate(new RTCIceCandidate(msg.candidate));
          break;
        case 'ready':
          // Create input channel before creating offer so it's included in the offer
          log('🔧 [ready] Creating input channel before offer...');
          createInputChannel();

          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);
          socket.send(JSON.stringify({ type: 'offer', offer }));
          log('✅ [ready] Offer sent with input channel');
          break;
        case 'interactivity-status':
          if (msg.status === 'paused') {
            pauseFrameSending();
          } else if (msg.status === 'enabled') {
            // Update crop box if provided
            // DO not update crop box here as it will be updated by the tensorflow model directly
            // if (msg.cropBox) {
            //   updateCropBox(msg.cropBox);
            // }
            resumeFrameSending();
          } else if (msg.status === 'completed') {
            log('Captcha solved, stopping streaming');
            stopStreaming();
          }
          break;
        case 'trigger-comparison':
          // Forward the trigger-comparison message to the captcha detector
          if (
            window.captchaDetector &&
            typeof window.captchaDetector.triggerScreenshotComparison === 'function'
          ) {
            log('Received trigger-comparison message, forwarding to captcha detector');
            window.captchaDetector.triggerScreenshotComparison();
          } else {
            error('Cannot trigger comparison: captchaDetector not available');
          }
          break;
        case 'request-frame':
          log('Received request-frame event, delegating to browser controller');
          if (window.browserController) {
            await window.browserController.requestNewFrame();
          } else {
            error('Browser controller not available for request-frame');
          }
          break;
      }
    });

    socket.addEventListener('error', (e) => {
      error('WebSocket error:', e);
    });
  }

  // These will be updated with actual viewport dimensions
  let FRAME_WIDTH = 800;
  let FRAME_HEIGHT = 600;
  let TOTAL_PIXELS = FRAME_WIDTH * FRAME_HEIGHT;

  let captchaDetectorCallback = null;
  let isCapturingForCaptchaDetector = false;
  let isSendingFrames = true; // Controls whether frames are sent to the client

  /**
   * Registers a callback function from the captcha detector to receive frames.
   * This enables push-based frame delivery for screenshot comparison.
   * Passes the frame dimensions to the callback along with the RGBA buffer.
   *
   * @param {Function} callback - Function to call with each frame's RGBA buffer and dimensions
   */
  function registerCaptchaDetectorCallback(callback) {
    captchaDetectorCallback = callback;
    log('Captcha detector callback registered');
  }

  /**
   * Starts capturing frames for the captcha detector.
   * When active, each frame processed by the transform stream will be sent to the captcha detector.
   */
  function startCapturingForCaptchaDetector() {
    isCapturingForCaptchaDetector = true;
    log('Started capturing frames for captcha detector');
  }

  /**
   * Stops capturing frames for the captcha detector.
   * Called when comparison is complete or times out.
   */
  function stopCapturingForCaptchaDetector() {
    isCapturingForCaptchaDetector = false;
    log('Stopped capturing frames for captcha detector');
  }

  /**
   * Pauses sending frames to the client.
   * Called when a significant change is detected by the captcha detector.
   */
  function pauseFrameSending() {
    if (!isSendingFrames) return; // Already paused

    isSendingFrames = false;
    log('Paused sending frames to client');

    // Notify the client that interactivity is paused
    if (socket?.readyState === WebSocket.OPEN) {
      socket.send(
        JSON.stringify({
          type: 'interactivity-status',
          status: 'paused',
          cropBox: {
            x: cropX,
            y: cropY,
            width: width,
            height: height,
          },
        }),
      );
    }
  }

  /**
   * Resumes sending frames to the client.
   * Called when the LLM responds with a new bounding box or when no significant change is detected.
   */
  function resumeFrameSending() {
    if (isSendingFrames) return; // Already sending

    isSendingFrames = true;
    log('Resumed sending frames to client');
  }

  /**
   * Converts a VideoFrame to a buffer containing YUV data.
   * This is the first step in processing frames for the captcha detector.
   * Supports both I420 (3 planes) and NV12 (2 planes) formats.
   *
   * @param {VideoFrame} frame - The video frame to convert
   * @returns {Uint8Array} Buffer containing YUV data
   */
  async function videoFrameToBuffer(frame) {
    performance.mark('buffer-start');

    const ySize = FRAME_WIDTH * FRAME_HEIGHT;
    const uvSize = FRAME_WIDTH * Math.floor(FRAME_HEIGHT / 2);

    // Check the frame format if available
    const format = frame.format || 'I420'; // Default to I420 if format is not available
    log(`Processing frame with format: ${format}`);

    let buffer;
    let layout;

    if (format === 'I420') {
      // I420 has 3 planes: Y, U, and V
      // U and V planes are each 1/4 the size of Y plane
      const uSize = (FRAME_WIDTH / 2) * (FRAME_HEIGHT / 2);
      const vSize = uSize;
      buffer = new Uint8Array(ySize + uSize + vSize);

      layout = [
        { offset: 0, stride: FRAME_WIDTH }, // Y plane
        { offset: ySize, stride: FRAME_WIDTH / 2 }, // U plane
        { offset: ySize + uSize, stride: FRAME_WIDTH / 2 }, // V plane
      ];
    } else if (format === 'NV12') {
      // NV12 has 2 planes: Y and interleaved UV
      buffer = new Uint8Array(ySize + uvSize);

      layout = [
        { offset: 0, stride: FRAME_WIDTH }, // Y plane
        { offset: ySize, stride: FRAME_WIDTH }, // UV plane
      ];
    } else {
      // For other formats, try with 3 planes (most common)
      const uSize = (FRAME_WIDTH / 2) * (FRAME_HEIGHT / 2);
      const vSize = uSize;
      buffer = new Uint8Array(ySize + uSize + vSize);

      layout = [
        { offset: 0, stride: FRAME_WIDTH }, // Y plane
        { offset: ySize, stride: FRAME_WIDTH / 2 }, // U plane
        { offset: ySize + uSize, stride: FRAME_WIDTH / 2 }, // V plane
      ];

      log(`Using default 3-plane layout for unknown format: ${format}`);
    }

    let actualFormat = format;

    try {
      await frame.copyTo(buffer, { layout });
    } catch (err) {
      error(`Failed to copy frame with ${layout.length}-plane layout:`, err);

      // If the first attempt failed and we didn't try the alternative layout yet, try the other common format
      if (layout.length === 3) {
        log('Retrying with 2-plane NV12 layout');
        const newBuffer = new Uint8Array(ySize + uvSize);
        const newLayout = [
          { offset: 0, stride: FRAME_WIDTH }, // Y plane
          { offset: ySize, stride: FRAME_WIDTH }, // UV plane
        ];

        try {
          await frame.copyTo(newBuffer, { layout: newLayout });
          buffer = newBuffer;
          actualFormat = 'NV12';
          log('Successfully copied frame with 2-plane layout');
        } catch (retryErr) {
          error('Failed with both 3-plane and 2-plane layouts:', retryErr);
          throw retryErr;
        }
      } else if (layout.length === 2) {
        log('Retrying with 3-plane I420 layout');
        const uSize = (FRAME_WIDTH / 2) * (FRAME_HEIGHT / 2);
        const vSize = uSize;
        const newBuffer = new Uint8Array(ySize + uSize + vSize);
        const newLayout = [
          { offset: 0, stride: FRAME_WIDTH }, // Y plane
          { offset: ySize, stride: FRAME_WIDTH / 2 }, // U plane
          { offset: ySize + uSize, stride: FRAME_WIDTH / 2 }, // V plane
        ];

        try {
          await frame.copyTo(newBuffer, { layout: newLayout });
          buffer = newBuffer;
          actualFormat = 'I420';
          log('Successfully copied frame with 3-plane layout');
        } catch (retryErr) {
          error('Failed with both 2-plane and 3-plane layouts:', retryErr);
          throw retryErr;
        }
      } else {
        throw err;
      }
    }

    performance.mark('buffer-end');
    performance.measure('CopyTo Buffer', 'buffer-start', 'buffer-end');

    logTime('CopyTo Buffer');

    return convertYUVToRGBA(buffer, actualFormat);
  }

  /**
   * Converts YUV data to RGBA format for use with the captcha detector.
   * This is the second step in processing frames for comparison.
   * Supports both I420 (3 planes) and NV12 (2 planes) formats.
   *
   * @param {Uint8Array} buffer - Buffer containing YUV data
   * @param {string} format - The format of the YUV data ('I420' or 'NV12')
   * @returns {Uint8Array} Buffer containing RGBA data
   */
  function convertYUVToRGBA(buffer, format = 'NV12') {
    performance.mark('yuv-start');

    const ySize = FRAME_WIDTH * FRAME_HEIGHT;
    const rgba = new Uint8Array(TOTAL_PIXELS * 4);

    // For I420 format (3 planes: Y, U, V)
    if (format === 'I420') {
      const uSize = (FRAME_WIDTH / 2) * (FRAME_HEIGHT / 2);
      const uOffset = ySize;
      const vOffset = ySize + uSize;

      for (let y = 0; y < FRAME_HEIGHT; y++) {
        for (let x = 0; x < FRAME_WIDTH; x++) {
          const i = y * FRAME_WIDTH + x;
          const yVal = buffer[i];

          // U and V planes are quarter size of Y plane
          const uvX = Math.floor(x / 2);
          const uvY = Math.floor(y / 2);
          const uvIndex = uvY * (FRAME_WIDTH / 2) + uvX;

          const u = buffer[uOffset + uvIndex] - 128;
          const v = buffer[vOffset + uvIndex] - 128;

          // YUV to RGB conversion
          const r = yVal + 1.402 * v;
          const g = yVal - 0.344136 * u - 0.714136 * v;
          const b = yVal + 1.772 * u;

          const rgbaIndex = i * 4;
          rgba[rgbaIndex] = Math.min(255, Math.max(0, r));
          rgba[rgbaIndex + 1] = Math.min(255, Math.max(0, g));
          rgba[rgbaIndex + 2] = Math.min(255, Math.max(0, b));
          rgba[rgbaIndex + 3] = 255;
        }
      }
    }
    // For NV12 format (2 planes: Y and interleaved UV)
    else {
      const uvOffset = ySize;

      for (let y = 0; y < FRAME_HEIGHT; y++) {
        for (let x = 0; x < FRAME_WIDTH; x++) {
          const i = y * FRAME_WIDTH + x;
          const yVal = buffer[i];

          // UV plane has interleaved U and V values
          const uvIndex = uvOffset + Math.floor(y / 2) * FRAME_WIDTH + (x & ~1);
          const u = buffer[uvIndex] - 128;
          const v = buffer[uvIndex + 1] - 128;

          // YUV to RGB conversion
          const r = yVal + 1.402 * v;
          const g = yVal - 0.344136 * u - 0.714136 * v;
          const b = yVal + 1.772 * u;

          const rgbaIndex = i * 4;
          rgba[rgbaIndex] = Math.min(255, Math.max(0, r));
          rgba[rgbaIndex + 1] = Math.min(255, Math.max(0, g));
          rgba[rgbaIndex + 2] = Math.min(255, Math.max(0, b));
          rgba[rgbaIndex + 3] = 255;
        }
      }
    }

    performance.mark('yuv-end');
    performance.measure('YUV to RGBA', 'yuv-start', 'yuv-end');
    logTime('YUV to RGBA');

    return rgba;
  }

  /**
   * Processes a video frame for the captcha detector.
   * Converts the YUV frame to an RGBA buffer and sends it to the captcha detector.
   * Includes performance tracking for frame conversion.
   *
   * @param {VideoFrame} frame - The video frame to process
   * @returns {Promise<boolean>} - Returns true if a significant change was detected, false otherwise
   */
  async function processFrameForCaptchaDetector(frame) {
    try {
      // Start timing the conversion
      performance.mark('frame-conversion-start');

      // Convert YUV frame to RGBA buffer
      const buffer = await videoFrameToBuffer(frame);

      // End timing the conversion
      performance.mark('frame-conversion-end');
      performance.measure('Frame Conversion', 'frame-conversion-start', 'frame-conversion-end');
      const conversionTime = performance.getEntriesByName('Frame Conversion').pop().duration;

      log(`Frame conversion took ${conversionTime.toFixed(2)} ms`);

      // Send the RGBA buffer and dimensions to the captcha detector
      if (captchaDetectorCallback) {
        await captchaDetectorCallback(buffer, {
          width: FRAME_WIDTH,
          height: FRAME_HEIGHT,
        });
      }

      // Simply return the current state of isSendingFrames (false means a change was detected)
      return !isSendingFrames;
    } catch (err) {
      error('Error processing frame for captcha detector:', err);
      return false;
    } finally {
      // Clean up performance marks
      performance.clearMarks('frame-conversion-start');
      performance.clearMarks('frame-conversion-end');
      performance.clearMeasures('Frame Conversion');
    }
  }

  function logTime(label) {
    const entry = performance.getEntriesByName(label).at(-1);
    if (entry) {
      console.log(`⏱️ ${label} took ${entry.duration.toFixed(2)} ms`);
    }
    // performance.clearMarks();
    // performance.clearMeasures();
  }

  async function startStreaming(viewPort, cropBox) {
    // Update frame dimensions based on viewport
    FRAME_WIDTH = viewPort.width;
    FRAME_HEIGHT = viewPort.height;
    TOTAL_PIXELS = FRAME_WIDTH * FRAME_HEIGHT;

    if (config.debug) {
      log(`Updated frame dimensions to ${FRAME_WIDTH}x${FRAME_HEIGHT}`);
    }

    // Get display media stream
    log('Requesting display media stream...');
    try {
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
          width: viewPort.width,
          height: viewPort.height,
        },
        preferCurrentTab: true,
      });
      log('Display media stream obtained successfully');
    } catch (err) {
      error('Failed to get display media stream:', err);
      throw err;
    }

    // Set up browser metrics via browser controller
    if (window.browserController) {
      await window.browserController.setupBrowserMetrics(viewPort);
    } else {
      error('Browser controller not available for browser metrics setup');
    }

    // Set up stream processor and generator
    const videoTrack = screenStream.getVideoTracks()[0];
    const processor = new MediaStreamTrackProcessor({ track: videoTrack });
    const generator = new MediaStreamTrackGenerator({ kind: 'video' });

    function makeEven(n) {
      return Math.floor(n / 2) * 2;
    }

    /**
     * Alignment Requirements for YUV 4:2:0:
      x and y must be even
      width and height must be even
     */
    cropRegion = {
      x: makeEven(cropBox.x),
      y: makeEven(cropBox.y),
      width: makeEven(cropBox.width),
      height: makeEven(cropBox.height),
    };

    // Create a transform stream to crop the video frames
    const transform = new TransformStream({
      async transform(frame, controller) {
        // Process frame for captcha detector if needed
        if (isCapturingForCaptchaDetector && captchaDetectorCallback) {
          try {
            const changeDetected = await processFrameForCaptchaDetector(frame);

            if (changeDetected) {
              log('Frame triggered significant change detection - not sending to controller');
              frame.close();
              return;
            }
          } catch (err) {
            error('Error in captcha detector processing:', err);
            // Continue with frame processing even if captcha detection fails
          }
        }

        if (isSendingFrames) {
          const { codedWidth, codedHeight } = frame;
          const safeCropRegion = {
            x: Math.min(cropRegion.x, codedWidth),
            y: Math.min(cropRegion.y, codedHeight),
            width: Math.min(cropRegion.width, codedWidth - cropRegion.x),
            height: Math.min(cropRegion.height, codedHeight - cropRegion.y),
          };

          try {
            const cropped = new VideoFrame(frame, {
              visibleRect: safeCropRegion,
              displayWidth: safeCropRegion.width,
              displayHeight: safeCropRegion.height,
              timestamp: frame.timestamp,
              duration: frame.duration,
            });
            controller.enqueue(cropped);
          } catch (e) {
            error('Failed to crop frame:', e, { safeCropRegion, codedWidth, codedHeight });
          }
        } else {
          // If we're not sending frames, log it at a lower frequency to avoid console spam
          if (frame.timestamp % 1000000 === 0) {
            log('Frame sending paused due to significant change detection');
          }
        }

        frame.close();
      },
    });

    processor.readable.pipeThrough(transform).pipeTo(generator.writable);
    const croppedTrack = generator;
    const stream = new MediaStream([croppedTrack]);

    // Add track to peer connection
    pc.addTrack(croppedTrack, stream);
    updateCropBox(cropBox);

    // Perform initial input field detection after streaming starts
    setTimeout(() => {
      log('🔍 [STARTUP] Performing initial input field detection...');
      detectInputFieldsInViewport(cropBox);
    }, 1000);
  }

  let inputChannel = null;
  let currentInputBoxRects = [];

  function createInputChannel() {
    try {
      log('🔧 [createInputChannel] Creating input data channel...');
      inputChannel = pc.createDataChannel('inputEvents', {
        ordered: true,
        maxRetransmits: 3,
      });

      inputChannel.onopen = () => {
        log('✅ [createInputChannel] Input channel opened successfully');
        log('🔧 [createInputChannel] Input channel state:', inputChannel.readyState);
      };

      inputChannel.onclose = () => {
        log('🔌 [createInputChannel] Input channel closed');
      };

      inputChannel.onerror = (error) => {
        error('❌ [createInputChannel] Input channel error:', error);
      };

      inputChannel.onmessage = async (event) => {
        try {
          log('📨 [createInputChannel] Received input event:', event.data);
          const data = JSON.parse(event.data);

          // Trigger screenshot comparison for clicks
          if (data.type === 'mousedown') {
            window.captchaDetector?.triggerScreenshotComparison?.();
          }

          // Delegate all input handling to browser controller
          if (window.browserController) {
            await window.browserController.handleInputEvent(data);
          } else {
            error('Browser controller not available for input handling');
          }
        } catch (err) {
          error('Failed to handle input event:', err);
        }
      };

      log('✅ [createInputChannel] Input channel setup completed');
    } catch (err) {
      error('❌ [createInputChannel] Failed to create input channel:', err);
      throw err;
    }
  }

  function stopStreaming() {
    log('Stopping stream');

    try {
      if (screenStream) {
        const tracks = screenStream.getTracks();
        if (tracks && tracks.length > 0) {
          log('Stopping ' + tracks.length + ' screen tracks');
          tracks.forEach((track) => {
            if (track.readyState === 'live') {
              track.stop();
            }
          });
        }
        screenStream = null;
      }

      if (socket && socket.readyState === WebSocket.OPEN) {
        try {
          socket.close();
        } catch (socketErr) {
          error('Error closing socket:', socketErr);
        }
      }

      if (pc) {
        try {
          pc.close();
        } catch (pcErr) {
          error('Error closing peer connection:', pcErr);
        }
        pc = null;
      }

      socket = null;
      window.socket = null;

      log('Streaming stopped successfully');
    } catch (err) {
      error('Error during streaming cleanup:', err);
    }
  }

  /**
   * Updates crop box and detects input fields within the viewport area
   * @param {Object} newCropBox - New crop box coordinates {x, y, width, height}
   */
  function updateCropBox(newCropBox) {
    cropX = newCropBox.x;
    cropY = newCropBox.y;
    width = newCropBox.width;
    height = newCropBox.height;

    cropRegion.x = Math.floor(newCropBox.x / 2) * 2;
    cropRegion.y = Math.floor(newCropBox.y / 2) * 2;
    cropRegion.width = Math.floor(newCropBox.width / 2) * 2;
    cropRegion.height = Math.floor(newCropBox.height / 2) * 2;

    log('Crop box updated:', newCropBox);

    // Detect input fields within the new crop box area
    detectInputFieldsInViewport(newCropBox);
  }

  /**
   * Detects all input fields within the specified viewport area and sends their coordinates
   * Uses a hybrid approach: direct detection in main document + postMessage communication for iframe inputs
   * @param {Object} viewportArea - Viewport area to search for input fields {x, y, width, height}
   */
  async function detectInputFieldsInViewport(viewportArea) {
    try {
      log('🔍 [INPUT DETECTION] Detecting input fields within viewport:', viewportArea);

      // Initialize input fields collection
      const allInputFields = [];
      let inputIndex = 0;

      /**
       * Detects input fields in the current document context
       * This function runs in each frame's context (main document and iframes)
       */
      function detectInputsInCurrentDocument(context = 'main') {
        const inputFields = [];

        try {
          // Get all input elements in current document, excluding button-type inputs
          const inputElements = document.querySelectorAll(
            'input:not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="image"]), textarea, select, [contenteditable="true"]',
          );

          inputElements.forEach((element) => {
            try {
              // Skip hidden or disabled elements
              if (
                element.offsetParent === null ||
                element.disabled ||
                getComputedStyle(element).visibility === 'hidden' ||
                getComputedStyle(element).display === 'none'
              ) {
                return;
              }

              const rect = element.getBoundingClientRect();

              // Calculate absolute coordinates considering iframe position
              let absoluteX = rect.left + window.scrollX;
              let absoluteY = rect.top + window.scrollY;

              // If we're in an iframe, add iframe offset to main document
              if (window !== window.top) {
                try {
                  // Get iframe element in parent document
                  const iframe = window.parent.document.querySelector('iframe');
                  if (iframe) {
                    const iframeRect = iframe.getBoundingClientRect();
                    absoluteX += iframeRect.left + window.parent.scrollX;
                    absoluteY += iframeRect.top + window.parent.scrollY;
                  }
                } catch (crossOriginErr) {
                  // For cross-origin iframes, we'll rely on postMessage to get correct coordinates
                  console.log(
                    'Cross-origin iframe detected, using postMessage for coordinate mapping',
                  );
                }
              }

              const absoluteRect = {
                x: absoluteX,
                y: absoluteY,
                width: rect.width,
                height: rect.height,
              };

              // Check if input field is within the viewport area
              if (
                absoluteRect.x >= viewportArea.x &&
                absoluteRect.y >= viewportArea.y &&
                absoluteRect.x + absoluteRect.width <= viewportArea.x + viewportArea.width &&
                absoluteRect.y + absoluteRect.height <= viewportArea.y + viewportArea.height &&
                absoluteRect.width > 0 &&
                absoluteRect.height > 0
              ) {
                inputFields.push({
                  id: element.id || 'input-' + inputIndex++,
                  tagName: element.tagName,
                  type: element.type || 'text',
                  x: absoluteRect.x,
                  y: absoluteRect.y,
                  width: absoluteRect.width,
                  height: absoluteRect.height,
                  placeholder: element.placeholder || '',
                  name: element.name || '',
                  context: context,
                  frameInfo: {
                    isIframe: window !== window.top,
                    url: window.location.href,
                  },
                });
              }
            } catch (elementErr) {
              console.warn('Error processing input element in ' + context + ':', elementErr);
            }
          });
        } catch (docErr) {
          console.warn('Error accessing document in ' + context + ':', docErr);
        }

        return inputFields;
      }

      // Detect inputs in main document
      const mainDocumentInputs = detectInputsInCurrentDocument('main');
      allInputFields.push(...mainDocumentInputs);

      // Set up postMessage listener to receive input data from iframes
      const messageHandler = (event) => {
        if (event.data && event.data.type === 'IFRAME_INPUT_DETECTION_RESULT') {
          const iframeInputs = event.data.inputFields || [];
          allInputFields.push(...iframeInputs);
          log(
            '📨 [INPUT DETECTION] Received input data from iframe:',
            iframeInputs.length,
            'inputs',
          );
        }
      };

      window.addEventListener('message', messageHandler);

      // Request input detection from all accessible iframes
      const iframes = document.querySelectorAll('iframe');
      const iframePromises = [];

      iframes.forEach((iframe, index) => {
        const iframePromise = new Promise((resolve) => {
          try {
            // Skip hidden iframes
            if (
              iframe.offsetParent === null ||
              getComputedStyle(iframe).visibility === 'hidden' ||
              getComputedStyle(iframe).display === 'none'
            ) {
              resolve();
              return;
            }

            // Get iframe position for coordinate mapping
            const iframeRect = iframe.getBoundingClientRect();
            const iframeOffsetX = iframeRect.left + window.scrollX;
            const iframeOffsetY = iframeRect.top + window.scrollY;

            // Use postMessage approach for all iframes (both same-origin and cross-origin)
            // This provides consistent behavior and relies on the CDP-injected helper script
            try {
              const requestData = {
                type: 'REQUEST_INPUT_DETECTION',
                viewportArea: viewportArea,
                iframeOffset: {
                  x: iframeOffsetX,
                  y: iframeOffsetY,
                },
                requestId: 'input-detection-' + Date.now() + '-' + index,
              };

              iframe.contentWindow?.postMessage(requestData, '*');

              // Set timeout for iframe response
              setTimeout(() => {
                resolve();
              }, 1000); // Wait 1 second for iframe response
            } catch (postMessageErr) {
              console.warn('Error sending postMessage to iframe:', postMessageErr);
              resolve();
            }
          } catch (err) {
            console.warn('Error processing iframe:', err);
            resolve();
          }
        });

        iframePromises.push(iframePromise);
      });

      // Wait for all iframe processing to complete
      await Promise.all(iframePromises);

      // Clean up message listener
      window.removeEventListener('message', messageHandler);

      // Update current input box rectangles
      currentInputBoxRects = allInputFields;
      log(
        '✅ [INPUT DETECTION] Total detected',
        allInputFields.length,
        'input fields:',
        allInputFields,
      );

      // Send input field coordinates to layout via WebSocket
      sendInputFieldsToLayout(allInputFields, viewportArea);
    } catch (err) {
      error('❌ [INPUT DETECTION] Failed to detect input fields:', err);
      currentInputBoxRects = [];
      sendInputFieldsToLayout([], viewportArea);
    }
  }

  /**
   * Sends detected input field coordinates to layout via WebSocket
   * @param {Array} inputFields - Array of detected input field coordinates
   * @param {Object} viewportArea - Viewport area that was searched
   */
  function sendInputFieldsToLayout(inputFields, viewportArea) {
    if (socket?.readyState === WebSocket.OPEN) {
      const message = {
        type: 'cropbox-update',
        cropBox: {
          x: cropX,
          y: cropY,
          width: width,
          height: height,
        },
        inputBoxRects: inputFields,
      };

      socket.send(JSON.stringify(message));
      log('📤 [INPUT DETECTION] Sent input fields to layout:', {
        inputFieldsCount: inputFields.length,
        cropBox: message.cropBox,
        inputFields: inputFields,
      });
    } else {
      log('❌ [INPUT DETECTION] Socket not ready, cannot send input fields to layout');
    }
  }

  window.screenCropper = {
    init,
    start,
    stopStreaming,
    updateCropBox,
    registerCaptchaDetectorCallback,
    startCapturingForCaptchaDetector,
    stopCapturingForCaptchaDetector,
    pauseFrameSending,
    resumeFrameSending,
  };
})();

// open ai wrapper - Kazeel's developer API
// open ai enriched by user data.

// developer.kazeel.com - b2b
// kazeel.com - b2c
// what these agents gonna do when you land in kazeel?
// each agent is going to have their own landing agent.. datahunter/dataaccess/campaigns. contextual to the user..
// kazeel.com what story does it paint?
// story together that makes sense for b2b and b2c
// b2c centric.. and b2b..

// where did the user come from? which CTA he clicked from the landing page?\
// based on that-- we customize our landing experience in the app.

// a generic flow - we can ask the same thing in the app as well while registering.
// see a list of agents.. select and setup! once done, we provide option to continue with otping into another agent ?
//or do you task based on your agent selection
