import { R2Bucket } from '@cloudflare/workers-types';
import { User } from '../user/User';
import { z } from 'zod';
import { AgentNamespace } from 'agents';
import { Connections } from '../agent/connection-agent';
import { ConnectionsWorkflowParams } from '../workflow/types';

export type Environment = {
  ENVIRONMENT: string;
  SUNNY_API_ENDPOINT: string;
  CONNECTIONS_WORKFLOW: Workflow<ConnectionsWorkflowParams>;
  BROWSERBASE_PROJECT_ID: string;
  BROWSERBASE_API_KEY: string;
  KAKU_API_ENDPOINT: string;
  KAKU_WS_ENDPOINT: string;
  OPENAI_API_KEY: string;
  HYPERBROWSER_API_KEY: string;
  AI_GATEWAY_URL: string;
  ICE_SERVERS: object[];
  Connections: AgentNamespace<Connections>;
  SCREENSHOTS_INBOUND_BUCKET: R2Bucket;
  TESTBROWSER: Fetcher;
};

export type KakuApp = {
  Bindings: Environment;
  Variables: {
    user?: User;
  };
};

export const TokenInfoSchema = z.object({
  xAuthToken: z.string(),
  csrfToken: z.string(),
});

export type TokenInfo = z.infer<typeof TokenInfoSchema>;

export type PlatformTypes =
  | 'facebook'
  | 'github'
  | 'google'
  | 'test'
  | 'login_test'
  | 'cloudflare'
  | 'mt';
