import { ErrorContext, ErrorSource, LogLevel } from './types';
import { ErrorCode } from '../web/localizationMessages';

export class ErrorCollector {
  static collectError(
    source: ErrorSource,
    errorCode: ErrorCode,
    technicalDetails: any,
    logLevel: LogLevel = 'error',
    additionalContext: {
      userId: string;
      platformId: string;
      sessionId?: string;
      step?: string;
      [key: string]: any;
    },
  ): ErrorContext {
    const errorContext: ErrorContext = {
      source,
      logLevel,
      errorCode,
      technicalDetails,
      timestamp: new Date().toISOString(),
      ...additionalContext,
    };
    const doId = `${additionalContext.userId}:${additionalContext.platformId}`;

    console[logLevel](`[ErrorCollector] ${source} [DO: ${doId}]:`, {
      errorCode,
      technicalDetails,
      context: additionalContext,
    });

    return errorContext;
  }

  static collectClientScriptError(
    scriptName: string,
    errorCode: ErrorCode,
    originalError: Error,
    sessionContext: {
      userId: string;
      platformId: string;
      sessionId?: string;
    },
  ): ErrorContext {
    return this.collectError(
      'script_injection',
      errorCode,
      {
        scriptName,
        error: {
          message: originalError.message,
          stack: originalError.stack,
          name: originalError.name,
        },
      },
      'error',
      sessionContext,
    );
  }

  static collectWorkflowError(
    stepName: string,
    errorCode: ErrorCode,
    originalError: any,
    sessionContext: {
      sessionId: string;
      userId: string;
      platformId: string;
    },
  ): ErrorContext {
    return this.collectError(
      'workflow',
      errorCode,
      {
        stepName,
        error: originalError,
      },
      'error',
      {
        ...sessionContext,
        step: stepName,
      },
    );
  }
}
