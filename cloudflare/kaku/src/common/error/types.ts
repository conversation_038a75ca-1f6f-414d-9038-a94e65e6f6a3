import { ErrorCode } from '../web/localizationMessages';

export type LogLevel = 'error' | 'warn' | 'info' | 'debug';

export type ErrorSource =
  | 'browser_connection'
  | 'script_injection'
  | 'captcha_detection'
  | 'screen_cropper'
  | 'workflow'
  | 'agent'
  | 'htmx'
  | 'network'
  | 'cdp';

export interface ErrorContext {
  source: ErrorSource;
  logLevel: LogLevel;
  errorCode: ErrorCode;
  technicalDetails: any;
  timestamp: string;
  userId: string;
  platformId: string;
  sessionId?: string;
  step?: string;
  additionalContext?: Record<string, any>;
}

export interface ProcessedError {
  userMessage: string;
  shouldReplaceCard: boolean;
  errorCode: ErrorCode;
  logLevel: LogLevel;
}

export interface ErrorDisplayOptions {
  replaceCard?: boolean;
  dismissible?: boolean;
  showRetry?: boolean;
  autoHide?: boolean;
}
